import asyncio
import loguru
import multiprocessing
from multiprocessing.managers import DictProxy
import traceback
import time
from datetime import datetime
from typing import List
import numpy as np

from config_loader import config
from python_logger import logger
from utilities import (
    get_timestamp,
    start_as_process,
    datetime_string_to_seconds,
    start_as_thread,
    put_in_bounded_queue,
)
from control_ptz import IPCamera


class RequestsThread:
    """
    Represents a thread/threads that handles and formats incoming requests before sending them to the AnalysisThread.
    """

    def __init__(
        self,
        camera_ids: List[int],
        gui_queue: multiprocessing.Queue,
        communication_queue: multiprocessing.Queue,
        picking_queue: multiprocessing.Queue,
        cameras_calibrated_queue: multiprocessing.Queue,
    ):
        self.camera_ids = camera_ids
        self.gui_queue = gui_queue
        self.communication_queue = communication_queue
        self.picking_queue = picking_queue
        self.cameras_calibrated_queue = cameras_calibrated_queue

        self.multiprocessing_manager = multiprocessing.Manager()
        self.camera_ids = config.get_setting("other", "camera_ids")
        self.current_image_datas = {camera_id: {} for camera_id in self.camera_ids}

        self.requests = []
        self.refill_requests = []
        self.request_images = {}
        self.busy_iterations = 0
        self.camera_delay = config.get_setting("other", "camera_delay")

    async def run(self):
        # Start image loops
        for camera_id in self.camera_ids:
            self.current_image_datas[camera_id] = self.multiprocessing_manager.dict()
            camera_process = CameraProcess(
                camera_id,
                self.current_image_datas[camera_id],
                self.gui_queue,
                self.cameras_calibrated_queue,
            )
            start_as_process(camera_process.run)
        logger.info("REQUESTS THREAD: Image loops started.")

        # Start request loop
        start_as_thread(self.pickpoint_requests_loop)
        logger.info("REQUESTS THREAD: request loop started.")

    async def get_next_request(self):
        """
        Handles getting new requests.
        """
        # Wait if queue is empty
        while len(self.requests) == 0:
            if self.busy_iterations > 10:
                logger.info("REQUESTS THREAD: Backlog cleared.")
                self.busy_iterations = 0
            await asyncio.sleep(0.01)

        # Get request
        request = self.requests.pop(0)
        requested_box = str(request[0])
        request_timestamp = request[2]
        requested_camera = int(str(requested_box)[1])
        requested_image = self.request_images[request_timestamp][requested_camera]

        # Give warnings if process does not get any breaks and is lagging behind
        self.busy_iterations += 1
        if self.busy_iterations > 10:
            logger.warning("REQUESTS THREAD: Busy!")
            if self.busy_iterations > 150:
                put_in_bounded_queue(
                    self.gui_queue,
                    {
                        "type": "error_log",
                        "error_message": "Lag in picking process!",
                        "timestamp": get_timestamp(format="%Y-%m-%d_%Hh:%Mm:%Ss"),
                    },
                    used_logger=logger,
                )
                logger.error("REQUESTS THREAD: Removing requests to avoid OOM!")
                self.requests = []
                self.busy_iterations = 0

        # Clear handled request images
        present_timestamps = [request[2] for request in self.requests]
        timestamps_to_delete = []
        for timestamp_key in self.request_images.keys():
            if timestamp_key not in present_timestamps:
                timestamps_to_delete += [
                    timestamp_key,
                ]
        for timestamp in timestamps_to_delete:
            self.request_images.pop(timestamp, None)

        # return request data
        return requested_box, requested_image

    async def pickpoint_requests_loop(self):
        """
        Reads from the picking queue to keep the requests list updated.
        """
        while True:
            try:
                await self.process_request()
            except Exception:
                logger.error("REQUESTS THREAD:  Unexpected pickpoint_requests_loop error.")
                logger.error(f"REQUESTS THREAD:  {traceback.format_exc()}")

    async def process_request(self) -> None:
        # Wait for request
        while self.picking_queue.empty():
            await asyncio.sleep(0.1)
        result = self.picking_queue.get()
        box_id = result["box_id"]
        priority = result["priority"]
        request_timestamp = result["timestamp"]
        logger.debug(f"New request: {result}")

        # Catch refilled boxes
        if request_timestamp == "refill":
            self.refill_requests += [
                box_id,
            ]
            return

        # Link new timestamps to images
        if request_timestamp not in self.request_images.keys():
            try:
                request_images_dict = await self.get_images_for_request(request_timestamp)
                self.request_images[request_timestamp] = request_images_dict
            except TimeoutError:
                put_in_bounded_queue(
                    self.gui_queue,
                    {
                        "type": "error_log",
                        "error_message": f"Could not process request for {box_id}!",
                        "timestamp": get_timestamp(format="%Y-%m-%d_%Hh:%Mm:%Ss"),
                    },
                    used_logger=logger,
                )
                return

        # Handle refilled boxes
        for refill_request in self.refill_requests:
            self.requests.append((refill_request, 10, request_timestamp))
        self.refill_requests = []

        # Add to analysis list
        self.requests.append((box_id, priority, request_timestamp))
        self.structure_requests()

    async def get_images_for_request(self, request_timestamp: str) -> dict[str, np.ndarray]:
        request_images_dict = {}
        for camera_id in self.camera_ids:
            request_images_dict[camera_id] = await self.get_image_for_request(
                camera_id, request_timestamp
            )
        return request_images_dict

    async def get_image_for_request(self, camera_id: str, request_timestamp: str) -> np.ndarray:
        request_timestamp_seconds = datetime_string_to_seconds(request_timestamp)
        timeout_threshold = 1.5  # seconds
        while datetime.now().timestamp() - request_timestamp_seconds < timeout_threshold:
            # Retrieve latest image
            latest_image_timestamp = self.current_image_datas[camera_id].get("timestamp", None)
            latest_image = self.current_image_datas[camera_id].get("image", None)

            # Handle edgecase where no image has been taken yet
            if latest_image is None:
                logger.warning(f"REQUESTS THREAD {camera_id}: No image available!")
                await asyncio.sleep(0.1)
                continue

            # Check if image is outdated
            time_difference = datetime_string_to_seconds(
                latest_image_timestamp
            ) - datetime_string_to_seconds(request_timestamp)
            if time_difference < min(self.camera_delay, 1):
                logger.warning(
                    f"REQUESTS THREAD {camera_id}: Outdated image! request: {request_timestamp}, image: {latest_image_timestamp}"
                )
                await asyncio.sleep(0.1)
                continue

            # Use image
            return latest_image

        # Timeout reached, request has become too old
        logger.error(f"REQUESTS THREAD {camera_id}: No new image available!")
        raise TimeoutError(f"No new image available for camera {camera_id}!")

    def structure_requests(self):
        """
        Group requests for the same box and give requests in order of priority
        """
        structured_requests = []
        top_priorities = {}
        top_timestamps = {}
        for request in self.requests:
            if request[0] not in top_priorities.keys():
                top_priorities[request[0]] = request[1]
            elif request[1] < top_priorities[request[0]]:
                top_priorities[request[0]] = request[1]
            if request[0] not in top_timestamps.keys():
                top_timestamps[request[0]] = request[2]
            elif datetime_string_to_seconds(request[2]) > datetime_string_to_seconds(
                top_timestamps[request[0]]
            ):
                top_timestamps[request[0]] = request[2]
        for requested_box in top_priorities.keys():
            structured_requests += [
                (
                    requested_box,
                    top_priorities[requested_box],
                    top_timestamps[requested_box],
                ),
            ]
        self.requests = sorted(structured_requests, key=lambda x: x[1])


class CameraProcess:
    """
    Represents a process that handles the cpu bound image retrieval and puts it in a queue.
    """

    def __init__(
        self,
        camera_id: str,
        data_dict: DictProxy,
        gui_queue: multiprocessing.Queue,
        cameras_calibrated_queue: multiprocessing.Queue,
        given_logger: "loguru.Logger" = logger,
    ):
        self.camera_id = camera_id
        self.data_dict = data_dict
        self.gui_queue = gui_queue
        self.cameras_calibrated_queue = cameras_calibrated_queue
        self.logger = given_logger

    async def run(self):
        self.logger.info(f"IMAGE PROCESS: Connecting to camera {self.camera_id}")
        await self.reconnect_camera()
        self.logger.info(f"IMAGE PROCESS: Starting image loop for camera {self.camera_id}")
        await self.image_loop()

    async def init_cam(self):
        """
        Starts the camera and frame queue.
        """
        camera_ip = config.get_setting(f"camera_{self.camera_id}", "ip")
        camera_user = config.get_setting(f"camera_{self.camera_id}", "username")
        camera_password = config.get_setting(f"camera_{self.camera_id}", "password")

        # Start camera frame queue
        try:
            self.logger.info(f"IMAGE PROCESS: Starting Image Capture {self.camera_id}")
            self.camera = IPCamera(camera_ip, camera_user, camera_password, self.logger)
            self.camera.connect()
            await asyncio.sleep(0.2)  # Give time to connect properly
            connection_test = await self.camera.request_picture()
            if connection_test is None:
                raise Exception(f"IMAGE PROCESS: connection_test {self.camera_id} failed")
            self.camera.start_stream()
        except Exception:
            self.logger.error(f"IMAGE PROCESS: Camera {self.camera_id} connection failed!")
            self.logger.debug(f"IMAGE PROCESS: {traceback.format_exc()}")
            put_in_bounded_queue(
                self.gui_queue,
                {
                    "type": "error_log",
                    "error_message": f"The camera connection for camera {self.camera_id} failed!",
                    "timestamp": get_timestamp(format="%Y-%m-%d_%Hh:%Mm:%Ss"),
                },
                used_logger=self.logger,
            )
            put_in_bounded_queue(
                self.gui_queue,
                {
                    "type": "modal",
                    "title": "Cameras not calibrated",
                    "message": "Cameras need to be recalibrated with box calibration!",
                },
                used_logger=self.logger,
            )
            put_in_bounded_queue(
                self.cameras_calibrated_queue,
                False,
                used_logger=self.logger,
            )
            raise Exception(f"IMAGE PROCESS: Camera {self.camera_id} connection failed!")

    async def image_loop(self):
        """
        Reads from the camera to keep the last available image updated.
        """
        exception_state = False
        while True:
            try:
                # Wait for next frame
                image = await self.wait_for_next_frame()
                str_date_time = get_timestamp()
                self.data_dict["image"] = image
                self.data_dict["timestamp"] = str_date_time
                exception_state = False
            except Exception:
                if not exception_state:
                    exception_state = True
                    self.logger.error(
                        f"IMAGE PROCESS {self.camera_id}: Unexpected image_loop error."
                    )
                    self.logger.error(f"IMAGE PROCESS {self.camera_id}: {traceback.format_exc()}")

    async def wait_for_next_frame(self):
        """
        Handles waiting for new frames.
        """
        timeout_counter = time.perf_counter()
        previous_image = self.data_dict.get("image", None)
        while True:
            image = await self.camera.get_next_frame()
            if image is not None and (
                previous_image is None or not np.array_equal(previous_image, image)
            ):
                return image
            await asyncio.sleep(0.01)
            current_time = time.perf_counter()
            if current_time - timeout_counter > 2:
                # If the camera has not returned a new frame after two seconds, then we have likely lost connection
                self.logger.warning(
                    f"IMAGE PROCESS {self.camera_id}: Lost camera connection, trying to reestablish connection."
                )
                await self.reconnect_camera()
                timeout_counter = time.perf_counter()

    async def reconnect_camera(self):
        """
        A camera reconnection loop.
        """
        first_try = True
        while True:
            # Close previous connection
            try:
                self.camera.exit()
            except Exception:
                pass
            # Try to connect
            try:
                await self.init_cam()
                if not first_try:
                    self.logger.info(f"IMAGE PROCESS {self.camera_id}: Reconnected")
                    put_in_bounded_queue(
                        self.gui_queue,
                        {
                            "type": "error_log",
                            "error_message": f"Connection restored for camera {self.camera_id}.",
                            "timestamp": get_timestamp(format="%Y-%m-%d_%Hh:%Mm:%Ss"),
                        },
                        used_logger=self.logger,
                    )
                break
            except Exception:
                self.logger.error(
                    f"IMAGE PROCESS {self.camera_id}: Camera connection failed, trying to reestablish connection."
                )
                first_try = False
            # Wait and try again
            await asyncio.sleep(2)


async def main_test():
    from utilities import put_in_bounded_queue, get_timestamp

    # Start the requests thread
    gui_queue = multiprocessing.Queue()
    communication_queue = multiprocessing.Queue()
    picking_queue = multiprocessing.Queue()
    camera_ids = config.get_setting("other", "camera_ids")
    requests_thread = RequestsThread(camera_ids, gui_queue, communication_queue, picking_queue)
    await requests_thread.run()

    # Send requests
    box_id = 1131
    priority = 10
    timestamp = get_timestamp()
    put_in_bounded_queue(
        picking_queue, {"box_id": box_id, "priority": priority, "timestamp": timestamp}
    )
    await asyncio.Future()


if __name__ == "__main__":
    asyncio.run(main_test())
