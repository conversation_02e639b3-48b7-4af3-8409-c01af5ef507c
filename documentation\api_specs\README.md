# OpenAPI docs with Redocly
```bash	
cd documentation/api_specs
```
## Installation
Make sure you have npm installed.
```bash	
npm i -g @redocly/cli@latest
```
Get a live preview and intelligent code completion for your OpenAPI docs with the [OpenAPI (Swagger) Editor](https://marketplace.visualstudio.com/items?itemName=42Crunch.vscode-openapi) VS Code extension.
## Build
```bash
redocly build-docs openapi.yml -o heliovision-pcdata-api.html
```
## View
```bash
start heliovision-pcdata-api.html
```
