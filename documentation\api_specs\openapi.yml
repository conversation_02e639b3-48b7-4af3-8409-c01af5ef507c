openapi: '3.0.2'
info:
  title: heliovision - PC Data
  version: '3.2.0'

tags:
  - name: Box - PC Data → Heliovision
    description: |-
      Boxes and their contents.
      Endpoint published by Heliovision, called by PC Data.
  - name: Box - Heliovision → PC Data
    description: |-
      Boxes their contents.
      Endpoint published by PC Data, called by Heliovision.
paths:
  /box/request_pick_points:
    post:
      summary: Request Pick Points
      description: |-
        Request to calculate and send one or more pick points for one or more specific boxes.\
        The response will be given when ready in a different request from Heliovision to PC Data, see `/box/pick_points`.\
        A call to this endpoint implies that the robot is not obstructing any box.
      tags:
        - Box - PC Data → Heliovision
      requestBody:
        description: The boxes for which to calculate pick points.
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                type: object
                properties:
                  boxId:
                    type: integer
                    description: The ID of the box.
                    example: 1
                    minimum: 1
                  priority:
                    type: integer
                    description: The priority of the calculation of the pick points. Lowest will be calculated first.
                    example: 1
                    minimum: 1
                    maximum: 10
      responses:
        '200':
          description: Request has been received and will be processed.
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    description: Error message
                    example: "Box ID XX not found."
        '500':
          description: Internal Server Error
        '503':
          description: Service Unavailable
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    description: Error message
                    example: "Could not connect to camera."
  /box/pick_points:
    post:
      summary: Send Pick Points
      description: |-
        Send the calculated pick points for one or more specific boxes to PC Data.\
        This is a response to a previous request to `/box/request_pick_points`.\
        Beware that a list of pick points can be empty if no pick points were found for that box.
      tags:
        - Box - Heliovision → PC Data
      requestBody:
        description: The calculated pick points for the boxes.
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                type: object
                properties:
                  boxId:
                    type: integer
                    description: The ID of the box.
                    example: 1
                    minimum: 1
                  pickPoints:
                    $ref: '#/components/schemas/PickPoints'
                  timestamp:
                    type: string
                    format: date-time
                    description: The date and time of the calculation of the pick points.
                    example: '2024-01-01T12:00:00.123456Z'
      responses:
        '200':
          description: OK
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    description: Error message
                    example: "Box ID XX not found."
        '500':
          description: Internal Server Error
  /box/position:
    put:
      summary: Send the position of all boxes
      description: |-
        Send the position of all boxes. This will be sent exactly once after each calibration.
      tags:
        - Box - Heliovision → PC Data
      requestBody:
        description: The position of all boxes.
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                type: object
                properties:
                  boxId:
                    type: integer
                    description: The ID of the box.
                    example: 1
                    minimum: 1
                  referenceFrame:
                    $ref: '#/components/schemas/ReferenceFrame'
      responses:
        '200':
          description: OK
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    description: Error message
                    example: "Box ID XX not found."
        '500':
          description: Internal Server Error
  /heartbeat:
    get:
      summary: Send a heartbeat ping to check the connection
      description: |-
        Send a heartbeat request, to check if a response arrives. This will be sent roughly every second.
      tags:
        - Heartbeat - Heliovision → PC Data
      responses:
        '200':
          description: OK
        '400':
          description: Bad Request
        '500':
          description: Internal Server Error
components:
  schemas:
    PickPoint:
      type: object
      properties:
        x:
          type: integer
          description: The X coordinate of the pick point relative to the box origin.
          example: 1
        y:
          type: integer
          description: The Y coordinate of the pick point relative to the box origin.
          example: 1
    PickPoints:
      description: List of pick points in order of preference. Maximum 10 pick points.
      type: array
      items:
        $ref: '#/components/schemas/PickPoint'
      example:
        - x: 1
          y: 1
        - x: 2
          y: 2
        - x: 3
          y: 3
    ReferenceFrame:
      type: object
      properties:
        x:
          type: number
          description: The X coordinate of the reference frame origin.
          example: 1.5
        y:
          type: number
          description: The Y coordinate of the reference frame origin.
          example: 1
        z:
          type: number
          description: The Z coordinate of the reference frame origin.
          example: 1
        rx:
          type: number
          description: The rotation around the X axis of the reference frame.
          example: 0
        ry:
          type: number
          description: The rotation around the Y axis of the reference frame.
          example: 0
        rz:
          type: number
          description: The rotation around the Z axis of the reference frame.
          example: 0
