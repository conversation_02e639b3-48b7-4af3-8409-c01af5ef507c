import cv2 as cv
import numpy as np
import os
import concurrent.futures
from functools import partial

from utilities import show
from coordinate_transformations import get_region
from config_loader import config


def transform_and_crop_to_box(image, box_id, camera_id, visualize=False):
    """
    Transform and crop a full image to the image of a given box.
    """
    region = get_region(box_id, camera_id)
    return transform_and_crop_to_region(image, region, visualize)


def transform_and_crop_to_region(image, region, visualize=False):
    """
    Transform and crop a full image to the image of a given region.
    """
    image_size = (200, 240)  # size of the transformed image
    image_size_x = image_size[0]
    image_size_y = image_size[1]
    new_corner_points = [
        [0, 0],
        [image_size_x, 0],
        [0, image_size_y],
        [image_size_x, image_size_y],
    ]
    transformation_matrix = cv.getPerspectiveTransform(
        np.array(region, dtype=np.float32), np.array(new_corner_points, dtype=np.float32)
    )
    region_image = cv.warpPerspective(image, transformation_matrix, (image_size_x, image_size_y))

    if visualize:
        img_show = np.copy(image)
        for val in region:
            cv.circle(img_show, (int(val[0]), int(val[1])), 5, (0, 255, 0), -1)
        show(img_show, window_name="input", scale=0.5, visualize=visualize)
        show(region_image, window_name="output", scale=1, visualize=visualize)

    return region_image


def create_background_mask(image, visualize=False):
    # Get template matching background mask
    background_match_mask = create_background_match_mask(image, visualize=visualize)

    # Get saturated foreground mask
    saturated_foreground_mask = create_saturated_foreground_mask(image, visualize=visualize)

    # Combine masks
    background_match_mask[saturated_foreground_mask == 255] = (
        0  # Remove foreground from background mask
    )
    show(background_match_mask, window_name="background_mask", scale=0.5, visualize=visualize)

    return background_match_mask


def create_saturated_foreground_mask(image, visualize=False):
    # Get saturation channel
    blur_full_image = cv.stackBlur(image, ksize=(5, 5))
    hsv_full_image = cv.cvtColor(blur_full_image, cv.COLOR_BGR2HSV)
    s_full_image = hsv_full_image[:, :, 1]
    show(s_full_image, window_name="s_full_image", scale=0.5, visualize=visualize)

    # Threshold for binary mask
    background_saturation_threshold = config.get_setting(
        "code_parameters", "background_saturation_threshold"
    )  # Increase this value to more strictly keep the background
    foreground_mask = cv.threshold(
        s_full_image, background_saturation_threshold, 255, cv.THRESH_BINARY
    )[1]
    show(foreground_mask, window_name="foreground_mask", scale=0.5, visualize=visualize)

    return foreground_mask


def create_background_match_mask(image, visualize=False):
    """
    Create a mask for a given image that separates the bottom of the boxes from the products in the foreground via tempalte matching.

    Args:
        image: the image to analyze.
    """
    # Get the templates
    background_template_folder = (
        f"{config.get_setting('folder_paths', 'calibration_folder')}/templates"
    )
    template_files = os.listdir(background_template_folder)
    template_files.sort()
    templates = [cv.imread(f"{background_template_folder}/{file}") for file in template_files]
    template_thresholds = config.get_setting(
        "code_parameters", "template_thresholds"
    )  # Each template has a different threshold, these are ordered alphabetically
    template_zip = list(zip(templates, template_thresholds))

    # Match the templates to the image in parallel
    match_template_partial = partial(match_template, image)
    mask_full = np.zeros(image.shape[:2], dtype=np.uint8)
    with concurrent.futures.ThreadPoolExecutor() as executor:
        results = list(
            executor.map(lambda tpl: match_template_partial(tpl[0], tpl[1]), template_zip)
        )

    # Combine masks
    for template_mask in results:
        mask_full = cv.bitwise_or(mask_full, template_mask)

    # Clean up the mask
    mask_full = cv.dilate(mask_full, np.ones((3, 3), np.uint8), iterations=2)
    mask_full = cv.erode(mask_full, np.ones((3, 3), np.uint8), iterations=2)
    show(mask_full, window_name="mask_full", scale=0.5, visualize=visualize)

    return mask_full


def match_template(image, template, threshold=0.5, visualize=False):
    show(image, window_name="image", scale=0.5, visualize=visualize)
    show(template, window_name="template", visualize=visualize)

    # Blur image
    image = cv.stackBlur(image, ksize=(3, 3))

    # Match template to image
    template_radius = (template.shape[0] - 1) // 2
    image = cv.copyMakeBorder(
        image,
        template_radius,
        template_radius,
        template_radius,
        template_radius,
        cv.BORDER_CONSTANT,
        value=(0, 0, 0),
    )  # Pad to keep correct size
    match = cv.matchTemplate(image, template, cv.TM_CCOEFF_NORMED)
    show(match, window_name="match", scale=0.5, visualize=visualize)

    # Smooth result
    match = cv.dilate(match, np.ones((9, 9), np.uint8), iterations=1)
    show(match, window_name="match", scale=0.5, visualize=visualize)

    # Threshold for binary mask
    match_mask = cv.threshold(match, threshold, 1, cv.THRESH_BINARY)[1]
    show(match_mask, window_name="match", scale=0.5, visualize=visualize)

    # Format mask
    match_mask = match_mask * 255
    match_mask = match_mask.astype(np.uint8)
    return match_mask


def main():
    from utilities import get_latest_file_or_0

    # Set image
    camera_id = 1
    robot_id = int(config.get_setting("other", "robot_id"))
    background_image_folder = f"{config.get_setting('folder_paths', 'calibration_folder')}/background_images/camera_{camera_id}"
    background_image_path = get_latest_file_or_0(background_image_folder)
    image = cv.imread(background_image_path)
    # image = cv.imread('data/images/test/calibration testing/2024-08-26_15-23-47-948901_camera_2.png')

    # Set boxes
    for i in [
        11,
        12,
        13,
        14,
        15,
        21,
        22,
        23,
        24,
        25,
        31,
        32,
        33,
        34,
        35,
        41,
        42,
        43,
        44,
        45,
        51,
        52,
        53,
        54,
        55,
    ]:
        box_id = 1000 * robot_id + 100 * camera_id + i

        # Test image -> box transformation
        transform_and_crop_to_box(image, box_id, camera_id, visualize=True)

        # # Test background mask
        # mask_full = create_background_mask(image, visualize=True)
        # transform_and_crop_to_box(mask_full, box_id, camera_id, visualize=True)


if __name__ == "__main__":
    main()
