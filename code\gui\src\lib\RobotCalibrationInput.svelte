<script>
    // Imports
    import { Col, Row } from "sveltestrap";

    export let name;
    export let givenValue;
</script>

<Row style='padding: 0px; padding-bottom: 2px; padding-top: 2px; margin: 0px;'>
    <Col style="max-width: 1.3em !important; padding: 0px; display: flex; justify-content: center; flex-direction: column;">
        <h6 style="text-align: center; font-size: 16px !important; margin: 0px;">
            {name}:
        </h6>
    </Col>
    <Col style="max-width: 6em !important; padding: 0px">
        <input class="form-control" bind:value={givenValue}/>
    </Col>
</Row>
