<script>
  // Imports
  import { PythonMessages, PythonModal } from "@heliovision/sveltestrap";
  import Layout2 from "./Layout2.svelte";

  // Arguments
  export let store;
  export let disableMessages = false;
  export let disableModal = false;
  let className = "";
  export { className as class };
  export let logo = undefined;
  export let project = undefined;
</script>
  
<Layout2 {logo} {project} class={className} {...$$restProps}>
  <svelte:fragment slot="not-main">
    {#if !disableMessages}
      <PythonMessages {store} />
    {/if}
    {#if !disableModal}
      <PythonModal {store} />
    {/if}
  </svelte:fragment>
  <slot />
</Layout2>
  