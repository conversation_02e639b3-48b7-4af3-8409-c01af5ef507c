<script>
    // Imports
    import { Card, Col, Row } from "sveltestrap";
    import { pythonStore } from "../stores";
    
    export let buttonStatus = true;
  
    const { sendCommand } = pythonStore;
    
    function turnOn() {
        if (buttonStatus) {
            return;
        }
        sendCommand("toggle_image_saving");
    }
    
    function turnOff() {
        if (!buttonStatus) {
            return;
        }
      sendCommand("toggle_image_saving");
    }
</script>

<style>
    button {
        font-size: 0.9em;
        width: 100%;
        display: flex;
        align-items: center;
        text-align: center;
        justify-content: center;
        padding: 0 20px;
        min-height: 28px;
        border: 0px;
        color: white;
    }

    .turnOnActive {
        background: #55BA2B !important;
    }

    .turnOnInactive {
        background: #B6EF9B !important;
    }

    .turnOffActive {
        background: #DD6666 !important;
    }

    .turnOffInactive {
        background: #FFC4C4 !important;
    }
</style>

<Card color="success" style="margin-top: 20px; margin-bottom: 20px; width: 11.4em; border-radius: 15px; overflow: hidden; border: 0px;">
    <Row style='margin: 0px; padding: 0px; display: flex; justify-content: center;'>
        <h6 style="margin: 0px; padding-top: 3px; padding-bottom: 3px; font-size: 0.9em; display: flex; justify-content: center; color: white; border-bottom: 1px solid black;">Save images</h6>
    </Row>
    <Row style='margin: 0px; padding: 0px; display: flex;'>
        <Col style='margin: 0px; padding: 0px; display: flex;'>
            <button
                on:click={turnOn}
                class:turnOnActive={buttonStatus}
                class:turnOnInactive={buttonStatus == false}
                >
                On
            </button>
        </Col>
        <Col style='margin: 0px; padding: 0px; display: flex;'>
            <button
                on:click={turnOff}
                class:turnOffActive={buttonStatus == false}
                class:turnOffInactive={buttonStatus}
                >
                Off
            </button>
        </Col>
    </Row>
</Card>
