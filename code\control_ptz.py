from typing import Optional
import numpy as np
from onvif import ONVIFCamera
import cv2 as cv
import time
import loguru
import os
import asyncio
from hikvisionapi import AsyncClient

from utilities import start_as_thread, get_timestamp
from python_logger import logger
from config_loader import config


class CameraControl:
    def __init__(self, ip: str, user: str, password: str, port: int = 80) -> None:
        self.__cam_ip = ip
        self.__cam_user = user
        self.__cam_password = password
        self.__cam_port = port

    def start_camera(self) -> None:
        self.__cam = ONVIFCamera(
            self.__cam_ip, self.__cam_port, self.__cam_user, self.__cam_password
        )
        self.__media = self.__cam.create_media_service()
        self.__ptz = self.__cam.create_ptz_service()
        self.__profile = self.__media.GetProfiles()[0]
        self.__token = self.__profile.token

    def get_ptz(self) -> list:
        request = self.__ptz.create_type("GetStatus")
        request.ProfileToken = self.__token
        ptz_status = self.__ptz.GetStatus(request)
        pan = ptz_status.Position.PanTilt.x
        tilt = ptz_status.Position.PanTilt.y
        zoom = ptz_status.Position.Zoom.x
        return [pan, tilt, zoom]

    def relative_move(self, pan: float, tilt: float, zoom: float) -> None:
        tilt = tilt / 2  # for some reason needed
        request = self.__ptz.create_type("RelativeMove")
        request.ProfileToken = self.__token
        request.Translation = {"PanTilt": {"x": pan, "y": tilt}, "Zoom": {"x": zoom}}
        resp = self.__ptz.RelativeMove(request)
        logger.info(f"relative_move response: {resp}")

    def absolute_move(self, pan: float, tilt: float, zoom: float) -> None:
        request = self.__ptz.create_type("AbsoluteMove")
        request.ProfileToken = self.__token
        request.Position = {"PanTilt": {"x": pan, "y": tilt}, "Zoom": {"x": zoom}}
        resp = self.__ptz.AbsoluteMove(request)
        logger.info(f"absolute_move response: {resp}")

    def get_preset_complete(self):
        request = self.__ptz.create_type("GetPresets")
        request.ProfileToken = self.__token
        ptz_get_presets = self.__ptz.GetPresets(request)
        return ptz_get_presets

    def go_to_preset(self, preset_position: str):
        presets = self.get_preset_complete()
        request = self.__ptz.create_type("GotoPreset")
        request.ProfileToken = self.__token
        for i, _ in enumerate(presets):
            str1 = str(presets[i].Name)
            if str1 == preset_position:
                request.PresetToken = presets[i].token
                resp = self.__ptz.GotoPreset(request)
                return resp
        return None


class IPCamera:
    def __init__(
        self, ip_adress: str, user: str, password: str, given_logger: "loguru.Logger" = logger
    ) -> None:
        self.ip_adress = ip_adress
        self.user = user
        self.password = password
        self.current_image = None
        self.logger = given_logger

    def start_stream(self):
        start_as_thread(self.thread_stream)

    def connect(self):
        self.cam = AsyncClient(f"http://{self.ip_adress}", self.user, self.password)

    def exit(self):
        self.cam = None

    async def thread_stream(self):
        while True:
            # Get picture from camera
            img = await self.request_picture()

            # Check image
            if img is None:
                continue
            if self.current_image is not None and np.all(img == self.current_image):
                continue

            # Send image
            self.current_image = img

    async def get_next_frame(self, timeout=0) -> Optional[np.ndarray]:
        # Wait for the next frame
        timer = time.perf_counter()
        while time.perf_counter() - timer <= timeout or timeout == 0:
            if self.current_image is None:
                await asyncio.sleep(0.01)
                continue
            img_copy = self.current_image.copy()
            self.current_image = None
            return img_copy

        # Return None if timeout occurred
        self.logger.warning("Timeout occurred while waiting for the next frame.")
        return None

    async def request_picture(self) -> Optional[np.ndarray]:
        # Request picture
        try:
            image_bytes = bytearray()
            async for chunk in self.cam.Streaming.channels[102].picture(
                method="get", type="opaque_data"
            ):
                if chunk:
                    image_bytes.extend(chunk)
        except Exception:
            self.logger.error("Failed to get image")
            return None

        # Convert bytes to a NumPy image
        try:
            np_arr = np.frombuffer(image_bytes, dtype=np.uint8)
            img = cv.imdecode(np_arr, cv.IMREAD_COLOR)
            return img
        except Exception:
            self.logger.error("Failed to decode image")
            return None

    async def show_video_with_imsaving(self):
        SCALE = 2

        while True:
            frame = await self.get_next_frame(10)
            cv.imshow("frame", frame[::SCALE, ::SCALE, :])
            k = cv.waitKey(1)
            if k == ord("q"):
                break
            elif k == ord("s"):
                date_str = get_timestamp()
                if not os.path.exists("data/images/storage"):
                    os.makedirs("data/images/storage")
                img_name = "data/images/storage/" + date_str + ".png"
                cv.imwrite(img_name, frame)
                self.logger.info("Image saved: " + img_name)
        cv.destroyAllWindows()


async def get_single_image(camera_id):
    camera_ip = config.get_setting(f"camera_{camera_id}", "ip")
    camera_user = config.get_setting(f"camera_{camera_id}", "username")
    camera_password = config.get_setting(f"camera_{camera_id}", "password")
    try:
        ip_camera = IPCamera(camera_ip, camera_user, camera_password)
        ip_camera.connect()
        logger.debug("requesting picture")
        image = await ip_camera.request_picture()
    except Exception:
        # Try again
        await asyncio.sleep(0.2)
        ip_camera = IPCamera(camera_ip, camera_user, camera_password)
        ip_camera.connect()
        logger.debug("requesting picture again")
        image = await ip_camera.request_picture()
    return image


def main():
    camera_id = "camera_2"

    # Start image capture
    camera_ip = config.get_setting(camera_id, "ip")
    camera_user = config.get_setting(camera_id, "username")
    camera_password = config.get_setting(camera_id, "password")
    ip_camera = IPCamera(camera_ip, camera_user, camera_password)
    ip_camera.connect()
    ip_camera.start_stream()
    start_as_thread(ip_camera.show_video_with_imsaving)

    # Start camera ptz control
    mycam = CameraControl(camera_ip, camera_user, camera_password)
    mycam.start_camera()
    start_ptz = mycam.get_ptz()
    logger.info("Start: " + str(start_ptz))

    # Home: [-0.926111, -0.606, 1.0]
    # Can only zoom to 0, 0.33, 0.66, or 1
    # mycam.absolute_move(-0.926111, -0.606, 1.0)


if __name__ == "__main__":
    main()
