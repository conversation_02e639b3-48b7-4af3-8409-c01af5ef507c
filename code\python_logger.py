import logging
import sys
import asyncio
from multiprocessing import Process
import datetime
from loguru import logger

LOG_TO_FILE = True
LOG_DIR = "./logs"
LOG_FILE = f"{LOG_DIR}/python"
FILE_ROTATION = "100 MB"
FILE_RETENTION = "6 months"
FILE_COMPRESSION = None
FILE_LOG_LEVEL = logging.DEBUG
CONSOLE_LOG_LEVEL = logging.DEBUG


def get_timestamp():
    """
    Gets a string of the current timestamp in the following format: year-month-day_hour-minute-second-fractionalpart.
    """
    timestamp = datetime.datetime.now().timestamp()
    date_time = datetime.datetime.fromtimestamp(timestamp)
    str_date_time = date_time.strftime("%Y-%m-%d_%H-%M-%S-%f")
    return str_date_time


def logger_prep(given_logger):
    given_logger.remove()  # Default "sys.stderr" sink is not picklable
    given_logger.add(
        sink=sys.stderr, enqueue=True, level=CONSOLE_LOG_LEVEL
    )  # Custom "sys.stderr" sink is picklable
    if LOG_TO_FILE:
        given_logger.add(
            sink=f"{LOG_FILE}-{get_timestamp()}.log",
            rotation=FILE_ROTATION,
            enqueue=True,
            level=FILE_LOG_LEVEL,
            retention=FILE_RETENTION,
            compression=FILE_COMPRESSION,
        )


async def main_test():
    logger_prep(logger)

    p = Process(
        target=worker, args=(logger,)
    )  # on windows we need to pass the logger when multiprocessing
    p.start()

    while True:
        await asyncio.sleep(1)
        logger.info("Hello from main")


def worker(slave_logger):
    async def worker_main():
        while True:
            await asyncio.sleep(1)
            slave_logger.info("Hello from worker")

    asyncio.run(worker_main())


if __name__ == "__main__":
    asyncio.run(main_test())
