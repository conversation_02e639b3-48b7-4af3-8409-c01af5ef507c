import asyncio
import sys
import os

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config_loader import config
from python_logger import logger
from python_REST_client import RESTClient


class PCDataClientMock(RESTClient):
    async def send_request(self, box_id, priority):
        json_data = [{"boxId": box_id, "priority": priority}]
        await self.send("box/request_pick_points", "POST", json_data=json_data)


async def server_test():
    priority = 1
    rest_client = PCDataClientMock("http://localhost:8009")
    logger.info("starting communications")
    while True:
        print("Give a box_id and then press Enter to continue...")
        user_input = input().strip()
        if user_input == "q":
            break
        await rest_client.send_request(user_input, priority)
    logger.info("communications finished")


async def server_test_all(camera_id):
    robot_id = int(config.get_setting("other", "robot_id"))
    for i in range(1, 6):
        for j in range(1, 6):
            box_id = robot_id * 1000 + camera_id * 100 + j * 10 + i
            priority = j

            rest_client = PCDataClientMock("http://localhost:8009")
            logger.info("starting communications")
            await rest_client.send_request(box_id, priority)
            logger.info("communications finished")


if __name__ == "__main__":
    asyncio.run(server_test())
    asyncio.run(server_test_all(1))
    asyncio.run(server_test_all(2))
    asyncio.run(server_test_all(3))
    asyncio.run(server_test_all(4))
