import numpy as np
import pytest

from visualisations import scale_color, segment_pickpoints_image


@pytest.fixture
def black_image():
    # Create a dummy image (100x100 pixels, 3 channels, black background)
    image = np.zeros((100, 100, 3), dtype=np.uint8)
    return image


@pytest.fixture
def white_image():
    # Create a dummy image (100x100 pixels, 3 channels, white background)
    image = np.ones((100, 100, 3), dtype=np.uint8) * 255
    return image


@pytest.fixture
def pickpoints():
    # Create dummy pickpoints
    return [(10, 10), (10, 30), (10, 50), (30, 10), (50, 50)]


@pytest.fixture
def segmentations():
    # Create a dummy segmentation (all pixels are part of the same segment)
    segmentation = np.ones((100, 100), dtype=bool)
    return [segmentation]


def test_draw_pickpoints_retains_image_properties(black_image, pickpoints, segmentations):
    result_image = segment_pickpoints_image(
        pickpoints,
        segmentations,
        black_image,
        visualize=False,
        max_amount_of_pickpoints_to_draw=3,
    )

    assert result_image.shape == black_image.shape, "Output image shape mismatch"
    assert result_image.dtype == black_image.dtype, "Output image dtype mismatch"
    assert np.array_equal(result_image[0, 0], black_image[0, 0]), (
        "Top-left pixel should remain unchanged"
    )
    assert np.array_equal(result_image[99, 99], black_image[99, 99]), (
        "Bottom-right pixel should remain unchanged"
    )


def test_draw_all_pickpoints_black_background(black_image, pickpoints, segmentations):
    result_image = segment_pickpoints_image(
        pickpoints,
        segmentations,
        black_image,
        visualize=False,
        max_amount_of_pickpoints_to_draw=len(pickpoints),
    )

    # # Uncomment the following lines to visualize the result
    # import cv2 as cv
    # cv.imshow("result_image", result_image)
    # cv.waitKey(0)

    for i, point in enumerate(pickpoints):
        assert tuple(result_image[point[1], point[0]]) == scale_color(len(pickpoints), i), (
            f"Pickpoint {i} color mismatch"
        )


def test_draw_all_pickpoints_white_background(white_image, pickpoints, segmentations):
    result_image = segment_pickpoints_image(
        pickpoints,
        segmentations,
        white_image,
        visualize=False,
        max_amount_of_pickpoints_to_draw=len(pickpoints),
    )

    # # Uncomment the following lines to visualize the result
    # import cv2 as cv
    # cv.imshow("result_image", result_image)
    # cv.waitKey(0)

    for i, point in enumerate(pickpoints):
        assert tuple(result_image[point[1], point[0]]) == scale_color(len(pickpoints), i), (
            f"Pickpoint {i} color mismatch"
        )


def test_draw_some_pickpoints(black_image, pickpoints, segmentations):
    max_amount_of_pickpoints_to_draw = 3

    result_image = segment_pickpoints_image(
        pickpoints,
        segmentations,
        black_image,
        visualize=False,
        max_amount_of_pickpoints_to_draw=max_amount_of_pickpoints_to_draw,
    )

    for i, point in enumerate(pickpoints[:max_amount_of_pickpoints_to_draw]):
        assert tuple(result_image[point[1], point[0]]) == scale_color(
            max_amount_of_pickpoints_to_draw, i
        ), f"Pickpoint {i} color mismatch"
    for i, point in enumerate(
        pickpoints[max_amount_of_pickpoints_to_draw:], start=max_amount_of_pickpoints_to_draw
    ):
        assert tuple(result_image[point[1], point[0]]) == (0, 0, 0), (
            f"Pickpoint {i} should not be drawn"
        )


def test_draw_no_pickpoints(black_image, pickpoints, segmentations):
    result_image = segment_pickpoints_image(
        pickpoints,
        segmentations,
        black_image,
        visualize=False,
        max_amount_of_pickpoints_to_draw=0,
    )

    assert np.array_equal(result_image, black_image), (
        "Output image should be unchanged when no pickpoints are provided"
    )
