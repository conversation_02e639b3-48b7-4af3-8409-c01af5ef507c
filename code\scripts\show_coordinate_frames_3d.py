import open3d as o3d
import numpy as np

from config_loader import config
from utilities import load_json, get_latest_file_or_0
from coordinate_transformations import (
    get_box_frame_transformation,
    get_box_origin_robot,
    transform_coordinate_image_to_robot,
    transform_coordinate_robot_to_box,
    transform_coordinate_box_to_box_frame,
)


def create_coordinate_frame(
    transform: np.ndarray = None, size: float = 1
) -> o3d.geometry.TriangleMesh:
    if transform is None:
        transform = np.eye(4)
    frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=size, origin=[0, 0, 0])
    frame.transform(transform)
    return frame


def create_point_cloud(points: np.ndarray, color: np.ndarray = None) -> o3d.geometry.PointCloud:
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    if color is not None:
        pcd.paint_uniform_color(color)
    return pcd


def get_polygon_connections(points: np.ndarray, color: np.ndarray = None) -> o3d.geometry.LineSet:
    lines = []
    for i in range(len(points)):
        lines.append([i, (i + 1) % len(points)])
    line_set = o3d.geometry.LineSet()
    line_set.points = o3d.utility.Vector3dVector(points)
    line_set.lines = o3d.utility.Vector2iVector(lines)
    if color is not None:
        line_set.colors = o3d.utility.Vector3dVector([color for _ in range(len(lines))])
    return line_set


def get_point_connections(
    points_start: np.ndarray, points_end: np.ndarray, color: np.ndarray = None
) -> o3d.geometry.LineSet:
    assert len(points_start) == len(points_end), "Mismatched point counts."
    all_points = np.vstack((points_start, points_end))
    num = len(points_start)

    lines = []
    for i in range(num):
        lines.append([i, i + num])
    line_set = o3d.geometry.LineSet()
    line_set.points = o3d.utility.Vector3dVector(all_points)
    line_set.lines = o3d.utility.Vector2iVector(lines)
    if color is not None:
        line_set.colors = o3d.utility.Vector3dVector([color for _ in range(num)])
    return line_set


def visualize_in_frame(
    world_points: np.ndarray, frame_matrix: np.ndarray, color: np.ndarray, visualize: bool = True
) -> list:
    frame = create_coordinate_frame(frame_matrix, size=0.28)
    world_points_hom = np.hstack([world_points, np.ones((len(world_points), 1))])
    frame_points_hom = (frame_matrix @ world_points_hom.T).T
    frame_points = frame_points_hom[:, :3]
    frame_pcd = create_point_cloud(frame_points, color=color)
    lines_frame = get_polygon_connections(frame_points, color=color)
    geometry_list = [frame, frame_pcd, lines_frame]
    if visualize:
        o3d.visualization.draw_geometries(geometry_list)
    return geometry_list


def visualize_in_world(
    world_points: np.ndarray, color: np.ndarray, visualize: bool = True
) -> list:
    frame_world = create_coordinate_frame(np.eye(4), size=0.28)
    world_pcd = create_point_cloud(world_points, color=color)
    lines_world = get_polygon_connections(world_points, color=color)
    grid_lines = create_xy_grid()
    geometry_list = [frame_world, world_pcd, lines_world, grid_lines]
    if visualize:
        o3d.visualization.draw_geometries(geometry_list)
    return geometry_list


def create_xy_grid(
    size: float = 1.0, n: int = 6, color: np.ndarray = None
) -> o3d.geometry.LineSet:
    if color is None:
        color = np.array([0.8, 0.8, 0.8], dtype=np.float32)
    linspace = np.linspace(-size, size, n + 1) * n / 2

    points = []
    lines = []
    for i in range(n + 1):
        for j in range(n + 1):
            # Horizontal line
            p1 = (linspace[0], linspace[i], linspace[j])
            p2 = (linspace[-1], linspace[i], linspace[j])
            idx1 = len(points)
            idx2 = idx1 + 1
            points.append(p1)
            points.append(p2)
            lines.append([idx1, idx2])

            # Vertical line
            p3 = (linspace[i], linspace[0], linspace[j])
            p4 = (linspace[i], linspace[-1], linspace[j])
            idx3 = len(points)
            idx4 = idx3 + 1
            points.append(p3)
            points.append(p4)
            lines.append([idx3, idx4])

            # Perpendicular line
            p5 = (linspace[i], linspace[j], linspace[0])
            p6 = (linspace[i], linspace[j], linspace[-1])
            idx5 = len(points)
            idx6 = idx5 + 1
            points.append(p5)
            points.append(p6)
            lines.append([idx5, idx6])

    line_set = o3d.geometry.LineSet()
    line_set.points = o3d.utility.Vector3dVector(points)
    line_set.lines = o3d.utility.Vector2iVector(lines)
    line_set.colors = o3d.utility.Vector3dVector([color] * len(lines))

    return line_set


def create_sphere(
    center: np.ndarray, radius: float, color: np.ndarray = None
) -> o3d.geometry.TriangleMesh:
    sphere = o3d.geometry.TriangleMesh.create_sphere(radius=radius)
    sphere.compute_vertex_normals()
    if color is not None:
        sphere.paint_uniform_color(color)
    sphere.translate(center)
    return sphere


def main():
    scaling = 1000
    visualization_set = []

    # Visualize world frame
    world_points = np.array(
        [
            [0.28, 0, 0],
            [0, 0.28, 0],
        ]
    )
    visualization_set += visualize_in_world(world_points, np.array([1, 0, 0]), visualize=False)

    # Visualize boxes
    system_id = 2
    camera_color = np.array([0, 0, 1], dtype=np.float32)
    for camera_id in [1, 2, 3, 4]:
        R = get_box_frame_transformation(camera_id)
        T = np.eye(4)
        T[:3, :3] = R

        box_color = np.array([0, 1, 0], dtype=np.float32)
        for box in [
            11,
            12,
            13,
            14,
            15,
            21,
            22,
            23,
            24,
            25,
            31,
            32,
            33,
            34,
            35,
            41,
            42,
            43,
            44,
            45,
            51,
            52,
            53,
            54,
            55,
        ]:
            T_box = T.copy()
            box_origin_rack = get_box_origin_robot(
                f"{system_id}{camera_id}{box}", camera_id=camera_id
            )
            T_box[:3, 3] = box_origin_rack / scaling
            visualization_set += visualize_in_frame(
                world_points, T_box, box_color + camera_color, visualize=False
            )
            box_color *= 0.9
        camera_color *= 0.7

    # Visualize markers
    row_locations_robot_root = (
        f"{config.get_setting('folder_paths', 'calibration_folder')}/row_locations_robot"
    )
    marker_points = []
    for camera_id in [1, 2, 3, 4]:
        row_locations_robot = load_json(
            get_latest_file_or_0(f"{row_locations_robot_root}/camera_{camera_id}")
        )
        marker_points += [point[:3] for row in row_locations_robot.values() for point in row]
    marker_pcd = create_point_cloud(np.asarray(marker_points) / scaling, color=[0, 0, 0])
    visualization_set += [
        marker_pcd,
    ]

    # Visualize pickpoints
    pickpoints = np.asarray(
        [
            [342.19157236, 1347.52206394, -681.35381861],
            [537.761517, 1142.12529327, -755.1371799],
            [464.33377171, 1186.71795738, -739.23507103],
        ]
    )
    pickpoints = pickpoints / scaling
    for pickpoint in pickpoints:
        pickpoint_sphere = create_sphere(pickpoint, 0.02, color=[1, 1, 0])
        visualization_set += [
            pickpoint_sphere,
        ]

    # Visualize image
    camera_id = 3
    row_id = 3
    column_id = 3
    n = 30
    image_coordinates = np.asarray(
        [[i * 2560 / n, j * 1440 / n] for i in range(n) for j in range(n)]
    )
    robot_coordinates = np.asarray(
        [
            transform_coordinate_image_to_robot(
                image_pickpoint, f"{system_id}{camera_id}{row_id}{column_id}", camera_id
            )
            for image_pickpoint in image_coordinates
        ]
    )
    for robot_coordinate in robot_coordinates:
        color = [0, 1, 0]
        box_coordinate = transform_coordinate_robot_to_box(
            robot_coordinate, f"{system_id}{camera_id}{row_id}{column_id}", camera_id
        )
        box_frame_coordinate = transform_coordinate_box_to_box_frame(box_coordinate, camera_id)
        if box_frame_coordinate[2] < 14 or box_frame_coordinate[2] > 16:
            color = [1, 0, 0]
        coordinate_sphere = create_sphere(robot_coordinate / scaling, 0.01, color=color)
        visualization_set += [
            coordinate_sphere,
        ]

    # Run visualizer
    o3d.visualization.draw_geometries(visualization_set)


if __name__ == "__main__":
    main()
