<script>
    import { Mo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Header } from "sveltestrap";

    export let message;
    export let confirmText = 'Yes';
    export let cancelText = 'No';
    export let onConfirm;
    export let onCancel;
</script>

<style>
    .confirm-button {
        background-color: #55BA2B;
        border: 2px solid #55BA2B;
        color: white;
        border-radius: 20px;
        padding: 0.5em 1em;
    }

    .cancel-button {
        background-color: #DE9872;
        border: 2px solid #DE9872;
        color: white;
        border-radius: 20px;
        padding: 0.5em 1em;
    }
</style>

<Modal isOpen={true} {...$$restProps}>
    <ModalHeader>
        <h3 style="font-weight: 500; font-size: 18px">{message}</h3>
    </ModalHeader>
    <ModalBody style="display: flex; justify-content: space-evenly; align-items: center;">
        <button on:click={onConfirm} class="confirm-button">{confirmText}</button>
        <button on:click={onCancel} class="cancel-button">{cancelText}</button>
    </ModalBody>
</Modal>
