import cv2 as cv
import numpy as np

from utilities import get_latest_file_or_0, load_json, show
from config_loader import config

# camera_image = cv.imread(r"code/config/calibration/calibration_ici_paris_1/background_images/camera_2/2024-09-30_12-28-03-284626.png")
# camera_image = cv.imread(r"data/test_images/25-01-28-calib/2024-12-03_10-34-42-138658_camera_2.png")
camera_image = cv.imread(
    r"data/test_images/25-01-28-pickpoints/2025-01-22_15-23-31-414556_box_1231.png"
)
camera_id = "2"

# Load marker locations
marker_locations_path = f"{config.get_setting('folder_paths', 'calibration_folder')}/row_locations_vision/camera_{camera_id}"
print(marker_locations_path)
marker_locations_file = get_latest_file_or_0(marker_locations_path)
print(marker_locations_file)
marker_locations = load_json(marker_locations_file)
marker_locations_list = [marker for row, markers in marker_locations.items() for marker in markers]

# Load box locations
box_locations_path = (
    f"{config.get_setting('folder_paths', 'calibration_folder')}/box_locations/camera_{camera_id}"
)
print(box_locations_path)
box_locations_file = get_latest_file_or_0(box_locations_path)
print(box_locations_file)
box_locations = load_json(box_locations_file)
box_locations_list = [
    np.array(points).reshape(-1, 1, 2) for box_id, points in box_locations.items()
]

# Show data
for marker in marker_locations_list:
    cv.circle(camera_image, (int(marker[0]), int(marker[1])), 10, (0, 255, 0), -1)
for box in box_locations_list:
    print(box)
    cv.polylines(camera_image, [box], True, (255, 0, 0), 3)
show(camera_image, window_name=f"camera_{camera_id}", scale=0.5)
