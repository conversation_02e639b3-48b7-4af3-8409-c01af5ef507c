<script>
  // Imports
  import PythonLayout2 from "./lib/smaller_logo/PythonLayout2.svelte";
  import { Spinner } from "sveltestrap";
  import { pythonStore } from "./stores";
  import LiveView from "./lib/LiveView.svelte";
  import LoginPage from "./lib/LoginPage.svelte";
  import CalibrationPage from "./lib/CalibrationPage.svelte";
  import CalibrationPageOperator from "./lib/CalibrationPageOperator.svelte";
  import BoxCalibrationPage from "./lib/BoxCalibrationPage.svelte";
  import RobotCalibrationPage from "./lib/RobotCalibrationPage.svelte";

  // Reactive variables
  $: isConnected = !!$pythonStore?._timestamp;
  $: navIndex = 0;
  $: calibrationIndex = 0;
</script>

<style>
  * {
    font-family: sans-serif;
    font-size: 16px;
  }
</style>

<PythonLayout2 project="" logo="heliovision" store={pythonStore}>
  {#if isConnected}
    <div style="margin-top: -36px;">
      {#if navIndex === 0}
        <LiveView bind:index={navIndex}/>
      {:else if navIndex === 1}
        {#if $pythonStore.data['calibrate_login'] === true}
          {#if calibrationIndex === 0 || calibrationIndex === 3}
              <CalibrationPage bind:index={navIndex} bind:calibrationIndex={calibrationIndex}/>
          {:else if calibrationIndex === 1}
              <BoxCalibrationPage bind:index={navIndex} bind:calibrationIndex={calibrationIndex}/>
          {:else if calibrationIndex === 2}
              <RobotCalibrationPage bind:index={navIndex} bind:calibrationIndex={calibrationIndex}/>
          {/if}
        {:else}
          {#if calibrationIndex === 0}
              <CalibrationPageOperator bind:index={navIndex} bind:calibrationIndex={calibrationIndex}/>
          {:else if calibrationIndex === 1}
              <BoxCalibrationPage bind:index={navIndex} bind:calibrationIndex={calibrationIndex}/>
          {:else if calibrationIndex === 3}
              <LoginPage bind:index={navIndex} bind:calibrationIndex={calibrationIndex}/>
          {/if}
        {/if}
      {/if}
    </div>
  {:else}
    <Spinner />
  {/if}
</PythonLayout2>
