"""
Test suite for gradient analysis functionality in pickpoint segmentation.

This module tests the inward gradient analysis algorithm that determines
whether depth gradients point inward along object contours, which is used
to assess the quality of segmentation masks for pickpoint selection.
"""

import numpy as np
import pytest
from unittest.mock import Mock

from gradient_analysis import compute_inward_gradient_score
from pickpoint_segmenter import PickpointSegmenter


@pytest.fixture
def segmenter():
    """Create a PickpointSegmenter instance for testing."""
    # Mock the SAM model to avoid loading heavy dependencies in tests
    mock_segmenter = PickpointSegmenter.__new__(PickpointSegmenter)
    mock_segmenter.predictor = Mock()
    return mock_segmenter


@pytest.fixture
def circular_dome_data():
    """Create synthetic test data with a raised circular dome."""
    height, width = 200, 200
    depth_image = np.ones((height, width), dtype=np.float32) * 100  # Background depth

    # Create a circular raised object in the center
    center_x, center_y = width // 2, height // 2
    radius = 40

    for y in range(height):
        for x in range(width):
            dist = np.sqrt((x - center_x) ** 2 + (y - center_y) ** 2)
            if dist <= radius:
                # Create a raised dome-like object
                height_factor = 1 - (dist / radius) ** 2
                depth_image[y, x] = 100 + 50 * height_factor  # Raised by up to 50 units

    # Create a mask for the circular object
    mask = np.zeros((height, width), dtype=bool)
    for y in range(height):
        for x in range(width):
            dist = np.sqrt((x - center_x) ** 2 + (y - center_y) ** 2)
            if dist <= radius - 5:  # Slightly smaller than the depth object
                mask[y, x] = True

    return mask, depth_image


@pytest.fixture
def perfect_square_data():
    """Create a white square on black background - should give perfect score."""
    height, width = 200, 200

    # Create black background (low depth)
    depth_image = np.zeros((height, width), dtype=np.uint8)

    # Create a white square in the center (high depth)
    square_size = 80
    start_x = (width - square_size) // 2
    start_y = (height - square_size) // 2
    end_x = start_x + square_size
    end_y = start_y + square_size

    depth_image[start_y:end_y, start_x:end_x] = 255  # White square (high depth)

    # Create mask for the square - use the SAME size as the depth square
    # This puts the contour exactly on the gradient edge
    mask = np.zeros((height, width), dtype=bool)
    mask[start_y:end_y, start_x:end_x] = True

    return mask, depth_image


@pytest.fixture
def inverted_square_data():
    """Create a black square on white background - should give low score."""
    height, width = 200, 200

    # Create white background (high depth)
    depth_image = np.ones((height, width), dtype=np.uint8) * 255

    # Create a black square in the center (low depth)
    square_size = 80
    start_x = (width - square_size) // 2
    start_y = (height - square_size) // 2
    end_x = start_x + square_size
    end_y = start_y + square_size

    depth_image[start_y:end_y, start_x:end_x] = 0  # Black square (low depth)

    # Create mask for the square - use the SAME size as the depth square
    mask = np.zeros((height, width), dtype=bool)
    mask[start_y:end_y, start_x:end_x] = True

    return mask, depth_image


@pytest.fixture
def empty_mask_data():
    """Create empty mask test data."""
    height, width = 100, 100
    mask = np.zeros((height, width), dtype=bool)
    depth_image = np.ones((height, width), dtype=np.uint8) * 128
    return mask, depth_image


@pytest.fixture
def small_mask_data():
    """Create very small mask test data."""
    height, width = 100, 100
    mask = np.zeros((height, width), dtype=bool)
    mask[50:52, 50:52] = True  # 2x2 mask
    depth_image = np.ones((height, width), dtype=np.uint8) * 128
    return mask, depth_image


@pytest.fixture
def half_good_mask_data():
    """Create a half good scenario: rectangular mask over mixed depth regions.

    The mask is twice as large in one direction as a white square, with:
    - A white square in the center (good gradient source)
    - Dark regions on the left and right sides (poor gradient sources)
    - The mask extends over all three regions, creating mixed gradient quality
    """
    height, width = 200, 200

    # Create a more complex depth pattern to ensure we get mixed gradient results
    depth_image = np.ones((height, width), dtype=np.uint8) * 128  # Gray background

    # Create a white square in the center (high depth) - same size as perfect_square_data
    square_size = 80
    start_x = (width - square_size) // 2
    start_y = (height - square_size) // 2
    end_x = start_x + square_size
    end_y = start_y + square_size

    depth_image[start_y:end_y, start_x:end_x] = 255  # White square (high depth)

    # Create areas with inverted gradients on the sides where the mask will extend
    # Left side: create a black region (low depth) where mask will extend
    left_start = start_x - 40
    left_end = start_x
    depth_image[start_y:end_y, left_start:left_end] = 50  # Dark region on left

    # Right side: create a black region (low depth) where mask will extend
    right_start = end_x
    right_end = end_x + 40
    depth_image[start_y:end_y, right_start:right_end] = 50  # Dark region on right

    # Create mask that is twice as large in the horizontal direction
    # This creates a "half good" scenario where:
    # - The left and right edges of the mask are in dark regions (poor/inverted gradients)
    # - The top and bottom edges align with the white square (good gradients)
    mask = np.zeros((height, width), dtype=bool)
    mask_width = square_size * 2  # Twice as wide as the square
    mask_start_x = (width - mask_width) // 2
    mask_end_x = mask_start_x + mask_width

    mask[start_y:end_y, mask_start_x:mask_end_x] = True

    return mask, depth_image


class TestGradientAnalysisSanityChecks:
    """Test the basic working of gradient_analysis module as a standalone component."""

    def test_standalone_gradient_computation(self):
        """Test that the module works independently."""
        # Create simple test data
        mask = np.zeros((50, 50), dtype=bool)
        mask[20:30, 20:30] = True

        depth_image = np.zeros((50, 50), dtype=np.uint8)
        depth_image[20:30, 20:30] = 255

        # Test the main function
        score = compute_inward_gradient_score(mask, depth_image)

        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score > 0.8  # Should be high for this simple case

    def test_module_with_different_input_formats(self):
        """Test that the module handles different input formats."""
        mask = np.zeros((30, 30), dtype=bool)
        mask[10:20, 10:20] = True

        # Test with grayscale image
        depth_gray = np.zeros((30, 30), dtype=np.uint8)
        depth_gray[10:20, 10:20] = 255

        score_gray = compute_inward_gradient_score(mask, depth_gray)
        assert 0.0 <= score_gray <= 1.0
        assert score_gray > 0.8  # Should be high for this simple case


class TestGradientAnalysis:
    """Test class for gradient analysis functionality."""

    def test_circular_dome_gradient_analysis(self, circular_dome_data):
        """Test gradient analysis on a circular dome - should detect inward gradients."""
        mask, depth_image = circular_dome_data

        score = compute_inward_gradient_score(mask, depth_image)

        # Should achieve high score for smooth inward gradients
        assert score >= 0.8, f"Expected high score for circular dome, got {score:.3f}"
        assert score <= 1.0, f"Score should not exceed 1.0, got {score:.3f}"

    def test_perfect_square_gradient_analysis(self, perfect_square_data):
        """Test gradient analysis on a perfect square - should achieve perfect score."""
        mask, depth_image = perfect_square_data

        score = compute_inward_gradient_score(mask, depth_image)

        # Should achieve perfect or near-perfect score
        assert score >= 0.95, f"Expected near-perfect score for white square, got {score:.3f}"
        assert score <= 1.0, f"Score should not exceed 1.0, got {score:.3f}"

    def test_inverted_square_gradient_analysis(self, inverted_square_data):
        """Test gradient analysis on inverted square - should give low score."""
        mask, depth_image = inverted_square_data

        score = compute_inward_gradient_score(mask, depth_image)

        # Should achieve low score for outward gradients
        assert score <= 0.2, f"Expected low score for inverted square, got {score:.3f}"
        assert score >= 0.0, f"Score should not be negative, got {score:.3f}"

    def test_empty_mask_gradient_analysis(self, empty_mask_data):
        """Test gradient analysis on empty mask - should return 0."""
        mask, depth_image = empty_mask_data

        score = compute_inward_gradient_score(mask, depth_image)

        assert score == 0.0, f"Expected 0.0 for empty mask, got {score:.3f}"

    def test_small_mask_gradient_analysis(self, small_mask_data):
        """Test gradient analysis on very small mask - should handle gracefully."""
        mask, depth_image = small_mask_data

        score = compute_inward_gradient_score(mask, depth_image)

        # Should return a valid score between 0 and 1
        assert 0.0 <= score <= 1.0, f"Score should be between 0 and 1, got {score:.3f}"

    def test_rectangular_mask_gradient_analysis(self, half_good_mask_data):
        """Test gradient analysis on rectangular mask over mixed depth regions - should give moderate score."""
        mask, depth_image = half_good_mask_data

        score = compute_inward_gradient_score(mask, depth_image)

        # Should achieve moderate score - some edges have good gradients, others don't
        # The top and bottom edges align with the white square (good inward gradients)
        # The left and right edges are in dark regions with poor/outward gradients
        # This creates a "half good" scenario with mixed gradient quality
        assert 0.2 <= score <= 0.6, (
            f"Expected moderate score for rectangular mask over mixed depth, got {score:.3f}"
        )
        assert score >= 0.0, f"Score should not be negative, got {score:.3f}"
        assert score <= 1.0, f"Score should not exceed 1.0, got {score:.3f}"

    def test_mask_extending_into_uniform_background(self):
        """Test that masks extending into uniform background get penalized."""
        height, width = 100, 100

        # Create uniform background
        depth_image = np.zeros((height, width), dtype=np.uint8)

        # Create a small white square
        depth_image[40:60, 40:60] = 255

        # Create mask that extends well beyond the white square
        mask = np.zeros((height, width), dtype=bool)
        mask[30:70, 20:80] = True  # Much larger than the 40:60, 40:60 square

        score = compute_inward_gradient_score(mask, depth_image)

        # The algorithm correctly ignores zero-gradient areas (uniform background)
        # and only evaluates areas with significant gradients
        # This test verifies the algorithm handles this case gracefully
        assert score == 0.0, (
            f"Expected 0.0 for mask extending into uniform background, got {score:.3f}"
        )

    def test_gradient_score_integration_in_mask_scoring(self, segmenter, perfect_square_data):
        """Test that gradient analysis is properly integrated into the PickpointSegmenter scoring pipeline.

        This is an integration test that verifies:
        1. The PickpointSegmenter.score_masks() method calls compute_inward_gradient_score()
        2. The gradient score is computed and stored in the mask dictionary
        3. The gradient score influences the final mask score (bonus/penalty system)

        This test ensures the gradient analysis isn't just a standalone function, but is
        actually used in the real segmentation workflow to improve mask quality assessment.

        The gradient score is stored as 'inward_gradient_fraction' in the mask dictionary
        and is used by the scoring system to provide bonuses for good gradients (≥0.8)
        and penalties for poor gradients (<0.3).
        """
        mask, depth_image = perfect_square_data

        # Create a complete mask dictionary that mimics real SAM output
        # This includes all fields that PickpointSegmenter.score_masks() expects
        test_mask = {
            "clean_segmentation": mask,
            "predicted_iou": 0.85,  # Moderate IoU to see gradient impact
            "area": np.count_nonzero(mask),
            "pickpoint": (100, 100),  # Center of 200x200 image
            "rrect": Mock(area=np.count_nonzero(mask)),
        }

        # Call the actual scoring method used in production
        # This should internally call compute_inward_gradient_score() and apply the result
        score = segmenter.score_masks(test_mask, None, depth_image)

        # Verify that gradient analysis was executed and results stored
        assert "inward_gradient_fraction" in test_mask, (
            "Gradient score should be computed and stored in mask dictionary"
        )
        gradient_score = test_mask["inward_gradient_fraction"]
        assert isinstance(gradient_score, (int, float)), (
            f"Expected numeric gradient score, got {type(gradient_score)}"
        )

        # For perfect square, gradient analysis should detect excellent inward gradients
        assert gradient_score >= 0.8, (
            f"Expected high gradient score for perfect square, got {gradient_score:.3f}"
        )

        # Verify the overall scoring system produces valid results
        assert isinstance(score, (int, float)), f"Expected numeric score, got {type(score)}"
        assert score > 0, f"Expected positive score, got {score:.3f}"


class TestGradientAnalysisEdgeCases:
    """Test edge cases for gradient analysis."""

    def test_boundary_points_handling(self):
        """Test that boundary points are properly skipped."""
        # Create mask with contour points at image boundaries
        mask = np.zeros((10, 10), dtype=bool)
        mask[0:5, 0:5] = True  # Top-left corner

        depth_image = np.ones((10, 10), dtype=np.uint8) * 128

        score = compute_inward_gradient_score(mask, depth_image)

        # Should handle boundary points gracefully
        assert 0.0 <= score <= 1.0, (
            f"Score should be valid despite boundary points, got {score:.3f}"
        )

    def test_no_significant_gradients(self):
        """Test handling when there are no significant gradients."""
        # Create uniform depth image (no gradients)
        mask = np.zeros((50, 50), dtype=bool)
        mask[20:30, 20:30] = True

        depth_image = np.ones((50, 50), dtype=np.uint8) * 128  # Uniform depth

        score = compute_inward_gradient_score(mask, depth_image)

        # Should return 0 when no significant gradients exist
        assert score == 0.0, f"Expected 0.0 for uniform depth, got {score:.3f}"

    def test_mask_shape_validation(self):
        """Test that different mask shapes are handled correctly."""
        # Test various mask shapes
        shapes = [
            (10, 10),  # Small square
            (100, 50),  # Rectangle
            (50, 100),  # Tall rectangle
            (200, 200),  # Large square
        ]

        for height, width in shapes:
            mask = np.zeros((height, width), dtype=bool)
            # Create a simple rectangular mask in the center
            h_start, h_end = height // 4, 3 * height // 4
            w_start, w_end = width // 4, 3 * width // 4
            mask[h_start:h_end, w_start:w_end] = True

            depth_image = np.ones((height, width), dtype=np.uint8) * 128

            score = compute_inward_gradient_score(mask, depth_image)

            # Should return valid score for all shapes
            assert 0.0 <= score <= 1.0, f"Invalid score {score:.3f} for shape {(height, width)}"


class TestGradientAnalysisPerformance:
    """Test performance and robustness of gradient analysis."""

    def test_gradient_analysis_performance(self, perfect_square_data):
        """Test that gradient analysis completes in reasonable time."""
        import time

        mask, depth_image = perfect_square_data

        start_time = time.perf_counter()
        score = compute_inward_gradient_score(mask, depth_image)
        end_time = time.perf_counter()

        # Should complete within 1 second for a 200x200 image
        assert end_time - start_time < 1.0, (
            f"Gradient analysis took too long: {end_time - start_time:.3f}s"
        )
        assert 0.0 <= score <= 1.0, f"Invalid score returned: {score:.3f}"

    def test_gradient_analysis_consistency(self, perfect_square_data):
        """Test that gradient analysis returns consistent results."""
        mask, depth_image = perfect_square_data

        # Run multiple times and check consistency
        scores = []
        for _ in range(5):
            score = compute_inward_gradient_score(mask, depth_image)
            scores.append(score)

        # All scores should be identical (deterministic algorithm)
        assert all(abs(score - scores[0]) < 1e-10 for score in scores), (
            f"Inconsistent scores: {scores}"
        )

    def test_gradient_analysis_with_noise(self):
        """Test gradient analysis robustness with noisy depth data."""
        height, width = 100, 100

        # Create base square
        depth_image = np.zeros((height, width), dtype=np.uint8)
        depth_image[25:75, 25:75] = 255

        # Add noise
        noise = np.random.randint(-20, 21, (height, width), dtype=np.int16)
        depth_image_noisy = np.clip(depth_image.astype(np.int16) + noise, 0, 255).astype(np.uint8)

        # Create mask
        mask = np.zeros((height, width), dtype=bool)
        mask[25:75, 25:75] = True

        score = compute_inward_gradient_score(mask, depth_image_noisy)

        # Should still detect some inward gradients despite noise
        assert 0.0 <= score <= 1.0, f"Invalid score with noise: {score:.3f}"
        # With noise, we expect lower but still positive score
        assert score >= 0.3, f"Score too low with noise: {score:.3f}"


class TestGradientAnalysisIntegration:
    """Integration tests for gradient analysis with the full scoring system."""

    def test_mask_scoring_with_gradient_bonus(self, segmenter, perfect_square_data):
        """Test that high gradient scores provide scoring bonuses."""
        mask, depth_image = perfect_square_data

        # Create a complete mask dictionary
        test_mask = {
            "clean_segmentation": mask,
            "predicted_iou": 0.85,  # Moderate IoU
            "area": np.count_nonzero(mask),
            "pickpoint": (100, 100),  # Center of 200x200 image
            "rrect": Mock(area=np.count_nonzero(mask)),
        }

        # Score the mask (this should compute and apply gradient score)
        score = segmenter.score_masks(test_mask, None, depth_image)

        # Verify gradient score was computed and stored
        assert "inward_gradient_fraction" in test_mask
        gradient_score = test_mask["inward_gradient_fraction"]
        assert isinstance(gradient_score, (int, float)), (
            f"Expected numeric gradient score, got {type(gradient_score)}"
        )
        assert gradient_score >= 0.8, (
            f"Expected high gradient score for perfect square, got {gradient_score:.3f}"
        )

        # Score should include gradient bonus
        base_score = 0.85  # Approximate base score from IoU
        assert score > base_score, f"Expected gradient bonus, got score {score:.3f}"

    def test_mask_scoring_with_gradient_penalty(self, segmenter, inverted_square_data):
        """Test that low gradient scores provide scoring penalties."""
        mask, depth_image = inverted_square_data

        # Create a complete mask dictionary
        test_mask = {
            "clean_segmentation": mask,
            "predicted_iou": 0.85,  # Moderate IoU
            "area": np.count_nonzero(mask),
            "pickpoint": (100, 100),  # Center of 200x200 image
            "rrect": Mock(area=np.count_nonzero(mask)),
        }

        # Score the mask (this should compute and apply gradient score)
        score = segmenter.score_masks(test_mask, None, depth_image)

        # Verify gradient score was computed and stored
        assert "inward_gradient_fraction" in test_mask
        gradient_score = test_mask["inward_gradient_fraction"]
        assert isinstance(gradient_score, (int, float)), (
            f"Expected numeric gradient score, got {type(gradient_score)}"
        )
        assert gradient_score <= 0.3, (
            f"Expected low gradient score for inverted square, got {gradient_score:.3f}"
        )

        # Test that the gradient penalty is applied by comparing with a high-gradient case
        # Create a perfect square for comparison
        perfect_mask = np.zeros((200, 200), dtype=bool)
        perfect_mask[60:140, 60:140] = True
        perfect_depth = np.zeros((200, 200), dtype=np.uint8)
        perfect_depth[60:140, 60:140] = 255

        perfect_test_mask = {
            "clean_segmentation": perfect_mask,
            "predicted_iou": 0.85,  # Same IoU
            "area": np.count_nonzero(perfect_mask),
            "pickpoint": (100, 100),
            "rrect": Mock(area=np.count_nonzero(perfect_mask)),
        }

        perfect_score = segmenter.score_masks(perfect_test_mask, None, perfect_depth)

        # The inverted square should score lower than the perfect square due to gradient penalty
        assert score < perfect_score, (
            f"Expected gradient penalty: inverted score {score:.3f} should be < perfect score {perfect_score:.3f}"
        )
