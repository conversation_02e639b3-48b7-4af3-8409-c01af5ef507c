<script>
  // Imports
  import { Card, Input, Col, Row } from "sveltestrap";
  import { pythonStore } from "../stores";

  export let index;
  export let calibrationIndex;
  let inputtedPassword = '';

  const { sendCommand } = pythonStore;

  function submitPassword() {
    sendCommand("submit_password", [inputtedPassword,]);
  }

  function turnBack() {
    index = 0;
    calibrationIndex = 0;
  }
</script>

<style>
  button {
    font-size: 0.9em;
    width: 7em;
    height: 2.5em;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    padding: 0 20px;
    min-height: 35px;
    border: 2px solid #55BA2B;
    border-radius: 20px;
    background: #55BA2B;
    color: white;
  }
</style>

<Col style="margin-left: 0.5em; margin-right: 0.5em; margin-top: 0px;">  
  <Row>
    <Card title="" color="primary-subtle" class="mt-4" style="display: flex; align-items: center;">
      <Col>  
        <Row>
          <h6 style="padding-top: 25px; padding-bottom: 8px; text-align: center; font-size: 16px !important;">
            Input the password in order to open the maintenance:
          </h6>
        </Row>
        <Row style="justify-content: space-around;">
          <Input
            bind:value={inputtedPassword}
            placeholder="Input password"
            style="width: 40%; text-align: center; font-size: 16px !important;"
          />
        </Row>
        <Row style="justify-content: space-around;">
          <button
            on:click={submitPassword}
            color="success"
            size="lg"
            style="margin-top: 20px; margin-bottom: 20px; width: 5.4em;"
          >
            Confirm
          </button>
        </Row>
      </Col>
    </Card>
  </Row>
  <Row style="justify-content: flex-end;">
    <button
      on:click={turnBack}
      size="lg"
      style="margin-top: 20px; margin-bottom: 20px; background-color:#85C5E0; border: 2px solid #85C5E0; width: 5em;"
    >
      Back
  </button>
  </Row>
</Col>
