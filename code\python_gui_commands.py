import asyncio
import time
from python_logger import logger
from python_svelte import PythonSvelte
import cv2 as cv
import numpy as np
import traceback
import multiprocessing

from config_loader import config
from utilities import (
    load_json,
    write_json,
    get_latest_file_or_0,
    put_in_bounded_queue,
    get_timestamp,
)
from calibration import (
    camera_to_box_calibration,
    save_row_locations_robot,
    get_box_positions,
)
from control_ptz import get_single_image


class PythonGUICommands(PythonSvelte):
    """
    Extends PythonSvelte to handle incoming and outgoing GUI commands.
    """

    def __init__(
        self,
        communication_queue: multiprocessing.Queue,
        picking_queue: multiprocessing.Queue,
        cameras_calibrated_queue: multiprocessing.Queue,
        *args,
        **kwargs,
    ):
        super(PythonGUICommands, self).__init__(*args, **kwargs)
        self.communication_queue = communication_queue
        self.picking_queue = picking_queue
        self.cameras_calibrated_queue = cameras_calibrated_queue
        self.password_timeout_timestamp = 0
        self.password_timeout_duration = 10
        self.adjustable_settings_path = config.get_setting(
            "folder_paths", "adjustable_settings_path"
        )
        self.image_saving_status = load_json(self.adjustable_settings_path)["image_saving_status"]
        self.calibration_password = config.get_setting("other", "calibration_password")
        self.camera_ids = [
            str(camera_id) for camera_id in config.get_setting("other", "camera_ids")
        ]
        if len(self.camera_ids) == 0:
            self.camera_ids = ["1"]

        # Calibration info
        self.box_calibration_current_camera_id = self.camera_ids[0]
        self.box_calibration_current_image = None
        self.robot_calibration_current_camera_id = self.camera_ids[0]
        self.robot_calibration_current_marker = 0
        self.row_locations_robot_root = (
            f"{config.get_setting('folder_paths', 'calibration_folder')}/row_locations_robot"
        )
        self.robot_calibration_current_row_locations = get_empty_robot_row_locations()

    async def start(self):
        # Run the GUI and the command processes in parallel
        logger.info("SERVER THREAD: Starting Main Threads")
        await asyncio.gather(self.process(), self.run_server())

    async def process(self):
        while True:
            try:
                # Start the Server
                logger.info("SERVER THREAD: Starting Server & acknowledging all commands")
                await self.reset_store()
                await self.set_state("running")
                await self.acknowledge_all_commands()
                logger.info("SERVER THREAD: Running Server")

                # Load configurations
                await self.update_data({"calibrate_login": False})
                await self.update_data({"box_calibration_success": False})
                await self.update_data({"box_calibration_finished": False})
                await self.update_data({"robot_calibration_copyable": False})
                await self.update_data({"robot_calibration_finished": False})
                await self.update_data({"errorLogs": []})
                await self.update_data({"last_timestamp": "Not yet available"})
                await self.update_data({"last_box_id": "0"})
                await self.update_data({"imageSavingStatus": self.image_saving_status})
                await self.initialize_box_status_table()
                await self.show_info_modal(
                    title="Program restarted",
                    message="The program has been restarted. The cameras need to be recalibrated with box calibration.",
                )
                await self.add_error_log(
                    "Program restarted. Cameras need to be recalibrated with box calibration.",
                    get_timestamp(format="%Y-%m-%d_%Hh:%Mm:%Ss"),
                )

                while True:
                    # Check for commands
                    logger.info("SERVER THREAD: Waiting for commands")
                    if len(self.commands) > 0:
                        command = self.commands[0]
                    else:
                        command = await self.await_new_command()
                    args = []
                    if isinstance(command, dict):
                        args = command.get("args", [])
                        command = command["command"]

                    # Process the command
                    logger.debug(f"SERVER THREAD: {command} {args}")
                    if command == "close_modal":
                        await self.force_close_modal()
                    # Box Calibration
                    elif command == "calibrate_box":
                        await self.calibrate_box()
                    elif command == "calibrate_box_next":
                        await self.calibrate_box_next()
                    elif command == "update_box_calibration_image":
                        await self.update_box_calibration_image()
                    elif command == "cancel_calibrate_box":
                        # TODO handle calibration cancellation
                        await self.reset_maintenance()
                    # Robot calibration
                    elif command == "calibrate_robot":
                        coordinates = args
                        await self.calibrate_robot(coordinates)
                    elif command == "calibrate_robot_copy":
                        coordinates = self.get_copied_robot_coordinates()
                        logger.info(f"SERVER THREAD: Copied robot coordinates: {coordinates}")
                        await self.calibrate_robot(coordinates)
                    elif command == "calibrate_robot_next":
                        await self.calibrate_robot_next()
                    elif command == "calibrate_robot_previous":
                        await self.calibrate_robot_previous()
                    elif command == "update_robot_calibration_image":
                        await self.update_robot_calibration_image()
                    elif command == "cancel_calibrate_robot":
                        # TODO handle calibration cancellation
                        await self.reset_maintenance()
                    # Password protection
                    elif command == "submit_password":
                        submitted_password = args[0]
                        await self.submit_password(submitted_password)
                    elif command == "log_out":
                        await self.reset_maintenance()
                    # Others
                    elif command == "refill_box":
                        box_id = args[0] if len(args) > 0 and len(args[0]) else None
                        if box_id is None:
                            logger.error("SERVER THREAD: No valid box given for refill_box")
                            await self.acknowledge_command(0)
                            continue
                        await self.update_box_status(box_id, "Full")
                        put_in_bounded_queue(
                            self.picking_queue,
                            {
                                "box_id": ici_id_to_vision_id(box_id),
                                "priority": 10,
                                "timestamp": "refill",
                            },
                        )
                    elif command == "refresh_box_positions":
                        await self.refresh_box_positions(notify=True)
                    elif command == "toggle_image_saving":
                        self.image_saving_status = not self.image_saving_status
                        await self.update_data({"imageSavingStatus": self.image_saving_status})
                        write_json(
                            self.adjustable_settings_path,
                            {"image_saving_status": self.image_saving_status},
                        )
                    # Unknown commands
                    else:
                        logger.error(f"SERVER THREAD: Unknown command: {command}")

                    # Acknowledge the command
                    await self.acknowledge_command(0)

            except Exception:
                logger.error(f"SERVER THREAD: Unexpected error: {traceback.format_exc()}")
                logger.info("SERVER THREAD: Restarting")

    async def calibrate_box(self):
        # Try calibration
        for i in range(3):
            try:
                camera_id = self.box_calibration_current_camera_id
                image = await get_single_image(camera_id)
                result_image = image.copy()
                await self.update_live_image("box_calibration_image", result_image)
                calibration_result = await camera_to_box_calibration(
                    camera_id, given_image=image, output_image=result_image
                )
                await self.update_live_image("box_calibration_image", result_image)
                if calibration_result:
                    await self.refresh_box_positions(notify=False)
                    break
            except Exception:
                logger.error(f"SERVER THREAD: Camera calibration error: {traceback.format_exc()}")
                calibration_result = False
        if not calibration_result:
            await self.show_info_modal(
                "Calibration markers not recognized!",
                "The calibration markers were not all recognized in the image. Ensure that the calibration markers are fully visible in the image and not obscured, and try again.",
            )
            return

        # Go to next camera
        await self.update_data({"box_calibration_success": True})

    async def calibrate_box_next(self):
        # Get camera index
        camera_id = self.box_calibration_current_camera_id
        camera_index = self.camera_ids.index(camera_id)

        # Go to next camera
        if len(self.camera_ids) > camera_index + 1:
            self.box_calibration_current_camera_id = self.camera_ids[camera_index + 1]
            await self.update_data({"box_calibration_success": False})
            await self.update_box_calibration_image()
        # Finish if all cameras are done
        else:
            self.box_calibration_current_camera_id = self.camera_ids[0]
            await self.update_data({"box_calibration_finished": True})
            await self.show_info_modal(
                "Calibration finished.",
                "The calibration procedure has been successfully completed.",
            )
            put_in_bounded_queue(self.cameras_calibrated_queue, True, used_logger=logger)

    async def update_box_calibration_image(self):
        camera_id = self.box_calibration_current_camera_id
        try:
            image = await get_single_image(camera_id)
            await self.update_live_image("box_calibration_image", image)
        except Exception:
            logger.error(f"SERVER THREAD: No valid calibration image: {traceback.format_exc()}")
            await self.show_info_modal("Service unavailable.")

    async def calibrate_robot(self, coordinates):
        # Update data
        logger.debug(
            f"SERVER THREAD: Calibrating robot at {self.robot_calibration_current_camera_id} at {self.robot_calibration_current_marker}"
        )
        coordinates = [0 if value is None else float(value) for value in coordinates]
        current_corner = index_to_corner_tuple(self.robot_calibration_current_marker)
        self.robot_calibration_current_row_locations[str(current_corner[0])][current_corner[1]] = (
            coordinates
        )

        # Go to next marker
        self.robot_calibration_current_marker += 1
        await self.update_robot_marker_copyable()

        # Save if all markers are done
        if self.robot_calibration_current_marker >= 20:
            try:
                # Test to verify the calibration
                box_positions = get_box_positions(
                    self.robot_calibration_current_camera_id,
                    self.robot_calibration_current_row_locations,
                )
            except Exception:
                logger.error(
                    f"SERVER THREAD: Invalid robot interpolation: {traceback.format_exc()}"
                )
                self.robot_calibration_current_marker = 0
                await self.update_robot_marker_copyable()
                await self.update_robot_calibration_image()
                await self.show_info_modal(
                    "Some of the robot coordinates were invalid, please try again."
                )
                return
            save_row_locations_robot(
                self.robot_calibration_current_row_locations,
                self.robot_calibration_current_camera_id,
            )
            put_in_bounded_queue(
                self.communication_queue,
                {
                    "type": "position",
                    "box_positions": box_positions,
                },
            )
            await self.calibrate_robot_next()
            if self.robot_calibration_current_camera_id != self.camera_ids[0]:
                await self.show_info_modal(
                    "Rack successfully calibrated. Calibration continues with the next rack."
                )
            return
        else:
            await self.update_robot_calibration_image()

    async def calibrate_robot_next(self):
        # Get camera index
        camera_id = self.robot_calibration_current_camera_id
        camera_index = self.camera_ids.index(camera_id)

        # Go to next camera
        if len(self.camera_ids) > camera_index + 1:
            self.robot_calibration_current_camera_id = self.camera_ids[camera_index + 1]
            self.robot_calibration_current_marker = 0
            await self.update_robot_marker_copyable()
            self.robot_calibration_current_row_locations = get_empty_robot_row_locations()
            await self.update_robot_calibration_image()
        # Finish if all cameras are done
        else:
            self.robot_calibration_current_camera_id = self.camera_ids[0]
            self.robot_calibration_current_marker = 0
            await self.update_robot_marker_copyable()
            self.robot_calibration_current_row_locations = get_empty_robot_row_locations()
            await self.update_data({"robot_calibration_finished": True})
            await self.show_info_modal(
                "Calibration finished.",
                "The calibration procedure has been successfully completed.",
            )

    async def calibrate_robot_previous(self):
        # Go to previous marker in order to fix a mistake/typo
        if self.robot_calibration_current_marker == 0:
            logger.error("SERVER THREAD: Asked for previous marker, but current marker is 0")
            await self.show_info_modal("No previous marker available.")
            return
        self.robot_calibration_current_marker -= 1
        await self.update_robot_marker_copyable()
        await self.update_robot_calibration_image()

    async def update_robot_calibration_image(self):
        camera_id = self.robot_calibration_current_camera_id
        max_attempts = 5
        current_attempt = 1
        while current_attempt <= max_attempts:
            try:
                image = await get_single_image(camera_id)
                image = self.flag_marker(image)
                await self.update_live_image("robot_calibration_image", image)
                break
            except Exception:
                logger.error(
                    f"SERVER THREAD: No valid calibration image: {traceback.format_exc()}"
                )
                current_attempt += 1
                await asyncio.sleep(1)
        if current_attempt >= max_attempts:
            await self.show_info_modal(
                title="Photo could not load.",
                message='Ensure that the camera is still connected and that the camera live view is not opened in the browser. Press the "Previous Marker" button and try again.',
            )

    def get_copied_robot_coordinates(self):
        # Get adjacent camera
        if int(self.robot_calibration_current_camera_id) == 1:
            adjacent_camera_id = 2
        elif int(self.robot_calibration_current_camera_id) == 2:
            adjacent_camera_id = 1
        elif int(self.robot_calibration_current_camera_id) == 3:
            adjacent_camera_id = 4
        elif int(self.robot_calibration_current_camera_id) == 4:
            adjacent_camera_id = 3
        else:
            return [None, None, None, None, None, None]

        # Get corresponding marker
        if self.robot_calibration_current_marker >= 10:
            corresponding_marker = self.robot_calibration_current_marker - 10
        else:
            corresponding_marker = self.robot_calibration_current_marker + 10

        # Get marker coordinates
        row_locations_robot_root = (
            f"{config.get_setting('folder_paths', 'calibration_folder')}/row_locations_robot"
        )
        row_locations_robot = load_json(
            get_latest_file_or_0(f"{row_locations_robot_root}/camera_{adjacent_camera_id}")
        )
        current_corner = index_to_corner_tuple(corresponding_marker)
        marker_coords = row_locations_robot[str(current_corner[0])][current_corner[1]]
        return marker_coords

    async def update_robot_marker_copyable(self):
        if int(self.robot_calibration_current_camera_id) in [2, 4]:
            if self.robot_calibration_current_marker >= 10:
                await self.update_data({"robot_calibration_copyable": True})
                return
        else:
            if self.robot_calibration_current_marker < 10:
                await self.update_data({"robot_calibration_copyable": True})
                return
        await self.update_data({"robot_calibration_copyable": False})

    async def refresh_box_positions(self, notify=True):
        for camera_id in self.camera_ids:
            box_positions = get_box_positions(camera_id)
            put_in_bounded_queue(
                self.communication_queue,
                {
                    "type": "position",
                    "box_positions": box_positions,
                },
            )
        if notify:
            await self.show_info_modal(
                title="Box positions are refreshed",
                message="The box positions are being synchronised with the latest robot calibration.",
            )

    async def submit_password(self, submitted_password):
        # Check the timeout
        timestamp = time.perf_counter()
        if timestamp - self.password_timeout_timestamp < self.password_timeout_duration:
            modal_message = f"Please wait {round(self.password_timeout_duration - (timestamp - self.password_timeout_timestamp))} seconds before trying again."
            await self.show_info_modal(title="Timeout", message=modal_message)
        else:
            # Check the password
            if submitted_password != self.calibration_password:
                self.password_timeout_timestamp = timestamp
                modal_message = (
                    f"Please wait {self.password_timeout_duration} seconds before trying again."
                )
                await self.show_info_modal(title="Incorrect Password", message=modal_message)
            else:
                self.login_timeout_timestamp = timestamp
                await self.show_info_modal(
                    title="Correct Password", message="Maintenance will be opened."
                )
                await self.update_data({"calibrate_login": True})

    async def reset_maintenance(self):
        self.box_calibration_current_camera_id = self.camera_ids[0]
        self.robot_calibration_current_camera_id = self.camera_ids[0]
        self.robot_calibration_current_marker = 0
        await self.update_robot_marker_copyable()
        self.robot_calibration_current_row_locations = load_json(
            get_latest_file_or_0(
                f"{self.row_locations_robot_root}/camera_{self.robot_calibration_current_camera_id}"
            )
        )
        await self.update_data({"box_calibration_success": False})
        await self.update_data({"box_calibration_finished": False})
        await self.update_data({"robot_calibration_finished": False})
        await self.update_data({"calibrate_login": False})

    async def show_info_modal(self, title, message=None):
        success = False
        while not success:
            logger.debug("SERVER THREAD: Try opening modal")
            success = await self.open_modal(
                modal_title=title,
                modal_body=message,
                modal_actions=[
                    {
                        "label": "Close",
                        "action": "close_modal",
                        "color": "primary",
                    },
                ],
                allow_close=True,
            )

    async def force_close_modal(self):
        success = False
        while not success:
            logger.debug("SERVER THREAD: Try closing modal")
            success = await self.close_modal()

    async def update_pickpoint_image(self, image):
        await self.update_live_image("last_pickpoint", image)

    async def add_error_log(self, error_message, timestamp):
        new_row = [error_message, timestamp]
        await self.update_data(
            {
                "errorLogs": [
                    new_row,
                ]
                + self.data.get("errorLogs", [])
            }
        )

    async def initialize_box_status_table(self):
        starting_statuses = [[f"{i:03}", "Unknown"] for i in range(1, 101)]
        await self.update_data({"boxStatus": starting_statuses})

    async def update_box_status(self, box_id, status):
        # Get current statuses
        statuses = self.data.get("boxStatus", [])

        # Get index positions of the box
        index = None
        for i, entry in enumerate(statuses):
            if entry[0] == str(box_id):
                index = i
                break
        if index is None:
            logger.error(f"SERVER THREAD: Could not find box {box_id} in statuses!")
            return
        del statuses[index]

        # Get index positions of the categories
        almost_empty_index = None
        full_index = None
        unknown_index = None
        for i, entry in enumerate(statuses):
            if entry[0] == str(box_id):
                index = i
            if almost_empty_index is None and entry[1] == "Almost empty":
                almost_empty_index = i
            if full_index is None and entry[1] == "Full":
                full_index = i
            if unknown_index is None and entry[1] == "Unknown":
                unknown_index = i

        # If a category is empty, use the next category's index
        if unknown_index is None:
            unknown_index = len(statuses)
        if full_index is None:
            full_index = unknown_index
        if almost_empty_index is None:
            almost_empty_index = full_index

        # Move the new status to the right position
        if status == "Empty":
            statuses.insert(almost_empty_index, [str(box_id), status])
        elif status == "Almost empty":
            statuses.insert(full_index, [str(box_id), status])
        elif status == "Full":
            statuses.insert(unknown_index, [str(box_id), status])
        elif status == "Unknown":
            statuses.insert(len(statuses), [str(box_id), status])
        await self.update_data({"boxStatus": statuses})

    def flag_marker(self, image):
        # Get marker locations
        marker_id = self.robot_calibration_current_marker
        camera_id = self.robot_calibration_current_camera_id
        row_locations_vision_root = (
            f"{config.get_setting('folder_paths', 'calibration_folder')}/row_locations_vision"
        )
        row_locations_vision = load_json(
            get_latest_file_or_0(f"{row_locations_vision_root}/camera_{camera_id}")
        )

        # Get current marker coords
        current_corner = index_to_corner_tuple(marker_id)
        marker_coords = row_locations_vision[str(current_corner[0])][current_corner[1]]

        # Flag marker
        flagged_image = cv.circle(image, np.asarray(marker_coords).astype(int), 45, (0, 255, 0), 8)
        return flagged_image


def index_to_corner_tuple(index):
    # Format marker locations: start in bottom-left, go up to top-left, then from bottom-right to top-right
    row = 5 - int((index % 10) / 2)  # Determine rack row
    subcolumn = index // 10  # Determine left or right
    subrow = 1 - (index % 2)  # Determine top or bottom
    corner_index = subrow + subcolumn * 2
    return (row, corner_index)


def get_empty_robot_row_locations():
    row_locations = {}
    for row in [1, 2, 3, 4, 5]:
        row_location_list = [[0, 0, 0, 0, 0, 0] for i in range(4)]
        row_locations[str(row)] = row_location_list
    return row_locations


def vision_id_to_ici_id(vision_id):
    """
    Converts a box ID in vision format to an ID in ici format, see readme for more info.
    """
    # Get fields
    vision_id = str(vision_id)
    camera_id = int(vision_id[1])
    row = int(vision_id[2])
    column = int(vision_id[3])

    # Calculate ici id
    box_number = (camera_id - 1) * 25 + (row - 1) * 5 + column
    return f"{box_number:03}"


def ici_id_to_vision_id(ici_id):
    """
    Converts a box ID in ici format to an ID in vision format, see readme for more info.
    """
    # Get fields
    ici_id = int(ici_id) - 1
    system_id = config.get_setting("other", "robot_id")
    camera_id = ici_id // 25 + 1
    row = (ici_id % 25) // 5 + 1
    column = (ici_id % 25) % 5 + 1
    return f"{system_id}{camera_id}{row}{column}"


# Run the main script
if __name__ == "__main__":
    import multiprocessing

    communication_queue = multiprocessing.Queue()
    picking_queue = multiprocessing.Queue()
    python_svelte = PythonGUICommands(
        communication_queue=communication_queue,
        picking_queue=picking_queue,
        initial_store={"state": "stopped"},
    )
    asyncio.run(python_svelte.start())
