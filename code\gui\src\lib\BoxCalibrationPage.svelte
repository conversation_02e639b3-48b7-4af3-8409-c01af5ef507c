<script>
  // Imports
  import { <PERSON>, Row } from "sveltestrap";
  import { pythonStore } from "../stores";
  
  export let index;
  export let calibrationIndex;
  
  const { sendCommand } = pythonStore;

  $: boxCalibrationImage = $pythonStore?.live_images?.['box_calibration_image'] || null;
  $: boxCalibrationSuccess = $pythonStore.data['box_calibration_success'] || false;
  $: boxCalibrationFinished = $pythonStore.data['box_calibration_finished'] || false;

  function calibrateBox() {
    sendCommand("calibrate_box");
  }

  function calibrateBoxNext() {
    sendCommand("calibrate_box_next");
  }

  function cancelCalibration() {
    sendCommand("cancel_calibrate_box");
    // Go back to the live view
    index = 0;
    calibrationIndex = 0;
  }

  function turnBack() {
    sendCommand("log_out");
    // Go back to the live view
    index = 0;
    calibrationIndex = 0;
  }
</script>

<style>
  button {
    font-size: 0.9em;
    width: 7em;
    height: 2.5em;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    padding: 0 20px;
    min-height: 35px;
    border: 2px solid #55BA2B;
    border-radius: 20px;
    background: #55BA2B;
    color: white;
  }
</style>

<Card title="" color="primary-subtle" class="mt-4" style="display: flex; align-items: center;">
  {#if !boxCalibrationFinished}
    {#if !boxCalibrationSuccess}
      <h6 style="padding-top: 25px; padding-bottom: 8px; text-align: center; font-size: 16px !important;">
        Check that all calibration markers are in view and click the Calibrate button.
      </h6>
      {#if boxCalibrationImage}
        <img src={boxCalibrationImage} alt="calibration_image" style="margin: 10px; width: 96% !important; max-width: 54em !important"/>
      {/if}
      <Row>
        <button
          on:click={calibrateBox}
          color="success"
          size="lg"
          style="margin-top: 20px; margin-bottom: 20px"
        >
          Calibrate
        </button>
        <button
          on:click={calibrateBoxNext}
          color="success"
          size="lg"
          style="margin-top: 20px; margin-bottom: 20px; margin-left: 20px; width: 11.2em; background-color:#85C5E0; border: 2px solid #85C5E0;"
        >
          Skip camera
        </button>
      </Row>
    {:else}
      <h6 style="padding-top: 25px; padding-bottom: 8px; text-align: center; font-size: 16px !important;">
        Camera successfully calibrated. Check the detected calibration markers and continue calibration with the next camera.
      </h6>
      {#if boxCalibrationImage}
        <img src={boxCalibrationImage} alt="calibration_image" style="margin: 10px; width: 96% !important; max-width: 54em !important"/>
      {/if}
      <Row>
        <button
          on:click={calibrateBoxNext}
          color="success"
          size="lg"
          style="margin-top: 20px; margin-bottom: 20px; margin-left: 20px; width: 11.2em; background-color:#85C5E0; border: 2px solid #85C5E0;"
        >
          Next camera
        </button>
    </Row>
    {/if}
  {:else}
    <h6 style="padding-top: 25px; padding-bottom: 8px; text-align: center; font-size: 16px !important;">
      Calibration finished.
    </h6>
    <button
      on:click={turnBack}
      style="margin-top: 20px; margin-bottom: 20px"
    >
      Back
    </button>
  {/if}
</Card>
{#if !boxCalibrationFinished}
  <Row style="justify-content: flex-end;">
    <button
      on:click={cancelCalibration}
      color="warning"
      size="lg"
      style="margin-top: 20px; margin-bottom: 20px; width: 5em; margin-right: 0.5em; background-color:#DE9872; border: 2px solid #DE9872;"
    >
      Cancel
    </button>
  </Row>
{/if}

