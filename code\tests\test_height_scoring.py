"""
Test suite for height-based scoring functionality in pickpoint segmentation.

This module tests the height-based scoring logic that uses depth values (min_depth and avg_depth)
to assess the quality of segmentation masks for pickpoint selection. The height score is calculated
as: score += min_depth / 255 + avg_depth / 255
"""

import numpy as np
import pytest
from unittest.mock import Mock

from pickpoint_segmenter import PickpointSegmenter


@pytest.fixture
def segmenter():
    """Create a PickpointSegmenter instance for testing."""
    # Mock the SAM model to avoid loading heavy dependencies in tests
    mock_segmenter = PickpointSegmenter.__new__(PickpointSegmenter)
    mock_segmenter.predictor = Mock()
    return mock_segmenter


@pytest.fixture
def high_depth_mask_data():
    """Create test data with high depth values (bright objects)."""
    height, width = 150, 150

    # Create depth image with high values (bright/close objects)
    depth_image = np.ones((height, width), dtype=np.uint8) * 200  # High depth background

    # Create a larger mask covering a region with high depth values (40x40 = 1600 pixels)
    mask = np.zeros((height, width), dtype=bool)
    mask[55:95, 55:95] = True  # 40x40 square mask, well within area requirements

    return mask, depth_image


@pytest.fixture
def low_depth_mask_data():
    """Create test data with low depth values (dark objects)."""
    height, width = 150, 150

    # Create depth image with low values (dark/far objects)
    depth_image = np.ones((height, width), dtype=np.uint8) * 50  # Low depth background

    # Create a larger mask covering a region with low depth values (40x40 = 1600 pixels)
    mask = np.zeros((height, width), dtype=bool)
    mask[55:95, 55:95] = True  # 40x40 square mask, well within area requirements

    return mask, depth_image


def test_depth_value_comparison(segmenter, high_depth_mask_data, low_depth_mask_data):
    """Test that higher depth values result in higher scores than lower depth values."""
    high_mask, high_depth_image = high_depth_mask_data
    low_mask, low_depth_image = low_depth_mask_data

    # Create identical mask dictionaries except for depth values
    high_test_mask = {
        "clean_segmentation": high_mask,
        "predicted_iou": 0.85,
        "area": np.count_nonzero(high_mask),
        "pickpoint": (75, 75),  # Center of mask (updated for 150x150 image)
        "rrect": Mock(area=np.count_nonzero(high_mask)),
    }

    low_test_mask = {
        "clean_segmentation": low_mask,
        "predicted_iou": 0.85,  # Same IoU
        "area": np.count_nonzero(low_mask),  # Same area
        "pickpoint": (75, 75),  # Same pickpoint (updated for 150x150 image)
        "rrect": Mock(area=np.count_nonzero(low_mask)),
    }

    # Score both masks with appropriate area constraints
    high_score = segmenter.score_masks(
        high_test_mask, None, high_depth_image, min_area=1000, max_area=5000
    )
    low_score = segmenter.score_masks(
        low_test_mask, None, low_depth_image, min_area=1000, max_area=5000
    )

    # High depth should result in higher score than low depth
    assert high_score > low_score, (
        f"High depth score {high_score:.3f} should be > low depth score {low_score:.3f}"
    )

    # Both scores should be finite and positive
    assert np.isfinite(high_score) and np.isfinite(low_score), (
        f"Both scores should be finite: high={high_score:.3f}, low={low_score:.3f}"
    )
    assert high_score > 0 and low_score > 0, (
        f"Both scores should be positive: high={high_score:.3f}, low={low_score:.3f}"
    )

    expected_difference = (200 + 200 - 50 - 50) / 255  # = 300/255 = 1.176471
    actual_difference = high_score - low_score
    assert abs(actual_difference - expected_difference) < 0.000001, (
        f"Score difference should be exactly {expected_difference:.6f}, got {actual_difference:.6f}"
    )


def test_depth_value_boundaries(segmenter):
    """Test scoring with depth values at boundaries (0, 255) - exact difference test."""
    height, width = 100, 100

    # Create larger masks that meet the area and gripper requirements
    # Test minimum depth values (0)
    depth_image_min = np.zeros((height, width), dtype=np.uint8)

    # Create a larger mask (40x40 = 1600 pixels) to meet area requirements
    mask = np.zeros((height, width), dtype=bool)
    mask[30:70, 30:70] = True  # 40x40 mask

    test_mask_min = {
        "clean_segmentation": mask,
        "predicted_iou": 0.85,
        "area": np.count_nonzero(mask),
        "pickpoint": (50, 50),  # Center should have good gripper radius
        "rrect": Mock(area=np.count_nonzero(mask)),
    }

    # Use relaxed area constraints to focus on height scoring
    score_min = segmenter.score_masks(
        test_mask_min, None, depth_image_min, min_area=1000, max_area=5000
    )

    # Test maximum depth values (255)
    depth_image_max = np.ones((height, width), dtype=np.uint8) * 255

    test_mask_max = {
        "clean_segmentation": mask,
        "predicted_iou": 0.85,
        "area": np.count_nonzero(mask),
        "pickpoint": (50, 50),
        "rrect": Mock(area=np.count_nonzero(mask)),
    }

    score_max = segmenter.score_masks(
        test_mask_max, None, depth_image_max, min_area=1000, max_area=5000
    )

    expected_difference = (255 + 255 - 0 - 0) / 255  # = 510/255 = 2.0
    actual_difference = score_max - score_min
    assert abs(actual_difference - expected_difference) < 0.000001, (
        f"Score difference should be exactly {expected_difference:.6f}, got {actual_difference:.6f}"
    )
