from typing import (
    Any,
    get_args,
    get_origin,
    Union,
    Tuple,
    List,
    Dict,
    Set,
    Optional,
    Callable,
)
from datetime import datetime
from pathlib import Path
import glob
import re
import asyncio
import logging
import multiprocessing
import tracemalloc
import threading
import os
import json
import pytz
import numpy as np
import cv2 as cv

from python_logger import logger

background_tasks = set()


def get_timestamp(format: str = "%Y-%m-%d_%H-%M-%S-%f") -> str:
    """
    Gets a string of the current timestamp in the given format. Default: year-month-day_hour-minute-second-fractionalpart.
    """
    timestamp = datetime.now().timestamp()
    date_time = datetime.fromtimestamp(timestamp)
    str_date_time = date_time.strftime(format)
    return str_date_time


def seconds_to_datetime_string(unix_seconds: float) -> str:
    datetime_seconds = datetime.fromtimestamp(unix_seconds)
    str_date_time = datetime_seconds.strftime("%Y-%m-%d_%H-%M-%S-%f")
    return str_date_time


def datetime_string_to_seconds(datetime_str: str) -> float:
    datetime_formats = [
        "%Y-%m-%d_%H-%M-%S-%f",
        "%Y-%m-%d_%H-%M-%S",
        "%d-%m-%Y_%H-%M-%S-%f",
        "%Y%m%d_%H-%M-%S-%f",
    ]
    timestamp = -1
    for datetime_format in datetime_formats:
        try:
            # Parse the datetime string into a datetime object
            dt_object = datetime.strptime(datetime_str, datetime_format)

            # Convert the datetime object to a Unix timestamp with fractional seconds
            timestamp = dt_object.timestamp()
        except Exception:
            # Try different format
            continue
        # Break on success
        break
    return timestamp


def datetime_string_to_day(datetime_str: str) -> str:
    datetime_seconds = datetime_string_to_seconds(datetime_str)
    date_time = datetime.fromtimestamp(datetime_seconds)
    str_date = date_time.strftime("%Y-%m-%d")
    return str_date


def parse_datetime_from_string(string: str) -> Optional[datetime]:
    # Define possible patterns for the datetime formats
    patterns = [
        (
            r"\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}-\d{6}",
            "%Y-%m-%d_%H-%M-%S-%f",
        ),  # with microseconds year first
        (
            r"\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}",
            "%Y-%m-%d_%H-%M-%S",
        ),  # without microseconds year first
        (r"\d{4}-\d{2}-\d{2}", "%Y-%m-%d"),  # date only year first
        (
            r"\d{2}-\d{2}-\d{4}_\d{2}-\d{2}-\d{2}-\d{6}",
            "%d-%m-%Y_%H-%M-%S-%f",
        ),  # with microseconds day first
        (
            r"\d{2}-\d{2}-\d{4}_\d{2}-\d{2}-\d{2}",
            "%d-%m-%Y_%H-%M-%S",
        ),  # without microseconds day first
        (r"\d{2}-\d{2}-\d{4}", "%d-%m-%Y"),  # date only day first
    ]

    # Match the patterns
    for pattern, date_format in patterns:
        match = re.search(pattern, string)
        if match:
            return datetime.strptime(match.group(0), date_format)
    return None


def seconds_to_iso8601(unix_seconds: float) -> str:
    datetime_seconds = datetime.fromtimestamp(unix_seconds)
    iso_date_time = datetime_seconds.isoformat()
    return iso_date_time


def iso8601_to_datetime(iso_string: str) -> datetime:
    # Check if the input string contains timezone info
    if iso_string.endswith("Z"):
        dt = datetime.strptime(iso_string, "%Y-%m-%dT%H:%M:%SZ")
        dt = dt.replace(tzinfo=pytz.UTC)
    elif "+" in iso_string or "-" in iso_string[19:]:
        dt = datetime.fromisoformat(iso_string)
    else:
        dt = datetime.strptime(iso_string, "%Y-%m-%dT%H:%M:%S")
        dt = dt.replace(tzinfo=pytz.UTC)

    return datetime.fromtimestamp(float(dt.timestamp()))


def show(
    img: np.ndarray,
    window_name: str = "image",
    scale: float = 1,
    visualize: bool = True,
    delay: float = 0,
) -> None:
    """
    :param img: The image to show
    :param scale: The scale factor
    """
    if not visualize:
        return
    img_shown = np.copy(img)
    if scale != 1:
        img_shown = cv.resize(img_shown, (0, 0), fx=scale, fy=scale)
    cv.imshow(window_name, img_shown)
    cv.waitKey(delay)


def between_callback_controller(async_function: Callable, args: tuple = ()) -> None:
    """
    A helper function to start an async function from a sync function.
    Starts the function for the given args.
    """
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.run_until_complete(async_function(*args))


def start_as_task(async_function: Callable, args: tuple = ()) -> type:
    """
    A helper function to start an async function as a seperate task from a sync function.
    Run a coroutine in the background without awaiting it.
    Starts the function for the given args.
    """
    task = asyncio.create_task(async_function(*args))

    # Keep track of the task to avoid automatic cleanup. Clean it up after completion
    background_tasks.add(task)
    task.add_done_callback(background_tasks.discard)

    return task


def start_as_thread(async_function: Callable, args: tuple = (), blocking: bool = False) -> None:
    """
    A helper function to start an async function as a seperate thread from a sync function.
    Starts the function for the given args.
    """
    thread = threading.Thread(target=between_callback_controller, args=(async_function, args))
    thread.start()
    if blocking:
        thread.join()


def start_as_process(async_function: Callable, args: tuple = ()) -> None:
    """
    A helper function to start an async function as a seperate process from a sync function.
    Starts the function for the given args.
    """
    multiprocessing.Process(target=start_as_thread, args=(async_function, args, True)).start()


def put_in_bounded_queue(
    queue: multiprocessing.Queue,
    item: type,
    max_items: int = 500,
    used_logger: logging.Logger = None,
) -> None:
    # Put item in queue
    queue.put(item)

    # Remove oldest items if queue is full
    while queue.qsize() > max_items:
        oldest = queue.get()
        if used_logger:
            # Put bound on the length of the string to avoid huge log files
            used_logger.warning(
                f"Removing {str(oldest)[:50]} from queue to avoid OOM! {queue.qsize()}"
            )


def peek_queue(queue: multiprocessing.Queue) -> type:
    # Temporarily remove the item from the queue
    item = queue.get()

    # Put it back into the queue
    queue.put(item)

    # Return the item that was peeked
    return item


def get_process_memory() -> int:
    mem_info = tracemalloc.get_traced_memory()
    return mem_info


def purge_directory(directory: str) -> None:
    # Check if the directory exists
    if not os.path.exists(directory):
        logger.info(f"The directory {directory} does not exist.")
        return

    # Iterate over all files and directories in the given directory
    for item in os.listdir(directory):
        item_path = os.path.join(directory, item)
        if os.path.isfile(item_path):
            os.remove(item_path)  # Remove the file
            logger.debug(f"Removed file: {item_path}")


def get_latest_file(folder: str, file_template: str = "*") -> str:
    # Standardize
    folder = str(Path(folder))

    # List all files in the specified directory
    files = glob.glob(f"{folder}/{file_template}")

    # Parse the timestamps
    latest_file = ""
    latest_date = datetime.min
    for file in files:
        file_date = parse_datetime_from_string(file)
        if file_date and file_date > latest_date:
            latest_file = file
            latest_date = file_date

    # Return the latest file
    return latest_file


def get_0_file(folder: str, file_template="*"):
    # Search for "0" file
    # Standardize
    if len(folder) > 0 and folder[-1] != "/":
        folder += "/"

    # List all files in the specified directory
    files_0 = glob.glob(f"{folder}{file_template}_0.*")

    # Return the first result if one is found
    if len(files_0) > 0:
        return files_0[0]
    return ""


def get_latest_file_or_0(folder: str, file_template="*"):
    # Get the latest file or the "0" file if no other valid file exists
    file = get_latest_file(folder, file_template)
    if file != "":
        return file
    else:
        # Search for "0" file
        return get_0_file(folder, file_template)


def load_json(json_file: str, used_logger: logging.Logger = logger) -> dict:
    if not os.path.isfile(json_file):
        used_logger.error(f"Cannot read json as file does not exist! {json_file}")
        raise Exception(f"Cannot read json as file does not exist! {json_file}")

    try:
        with open(json_file, "r") as f:
            json_data = json.load(f)
        return json_data
    except Exception:
        used_logger.error(f"File cannot be read as json! {json_file}")
        raise Exception(f"File cannot be read as json! {json_file}")


def write_json(file_path: str, json_data: dict, used_logger: logging.Logger = logger) -> None:
    folder = os.path.dirname(file_path)
    if not os.path.exists(folder):
        os.makedirs(folder)

    if os.path.isfile(file_path):
        used_logger.debug(f"Overwriting content in file {file_path}")

    try:
        with open(file_path, "w") as f:
            json.dump(json_data, f, indent=2)
    except Exception:
        used_logger.error(f"Could not write json data! {file_path}: {json_data}")
        raise Exception(f"Could not write json data! {file_path}: {json_data}")


def isinstance_parametrized(obj, type_spec):
    origin = get_origin(type_spec)

    # Non-parameterized types or special cases
    if origin is None:
        if type_spec is Any:
            return True  # Any matches any type
        elif isinstance(type_spec, type):
            return isinstance(obj, type_spec)
        return False
    if not isinstance(obj, origin):
        return False

    # Parameterized types
    args = get_args(type_spec)
    if origin in (list, List):
        # Check all elements in the list
        (element_type,) = args
        return all(isinstance_parametrized(elem, element_type) for elem in obj)
    elif origin in (tuple, Tuple):
        if len(args) == 2 and args[1] is Ellipsis:
            # Tuple[T, ...]: variable-length tuple of a single type
            element_type = args[0]
            return all(isinstance_parametrized(elem, element_type) for elem in obj)
        # Tuple[T1, T2, ...]: fixed-length tuple with specified types
        if len(obj) != len(args):
            return False
        return all(isinstance_parametrized(elem, arg_type) for elem, arg_type in zip(obj, args))
    elif origin in (dict, Dict):
        # Check all keys and values in the dict
        key_type, value_type = args
        return all(
            isinstance_parametrized(k, key_type) and isinstance_parametrized(v, value_type)
            for k, v in obj.items()
        )
    elif origin in (set, Set):
        # Check all elements in the set
        (element_type,) = args
        return all(isinstance_parametrized(elem, element_type) for elem in obj)
    elif origin is Union:
        # Check if object matches any of the Union types
        return any(isinstance_parametrized(obj, arg) for arg in args)

    # Fallback for other origins
    return isinstance(obj, origin)
