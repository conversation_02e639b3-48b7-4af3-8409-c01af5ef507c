# PC Data - Sample picking


## Installation

### Prerequisites:
- Python = 3.10
- pipenv
- git
- Node.js LTS
- Visual C++ Redistributable x64
- nssm.exe file or Ubuntu
- PC Data's software (If you want to actually operate a robot)

### Application setup:
- add user variable HV_PYPI_TOKEN with your api deploy token
- open terminal
- pipenv install

### GUI setup:
- go to code/gui folder
- create .npmrc file as instructed in https://git.heliovision.be/heliovision/toolbox/python_svelte
- open terminal
- cd code/gui
- npm install

### Runnables setup:
- Windows: create nssm services for start_gui, and start_main
- Ubuntu: 
  - edit the service files to contain the correct paths
  - ln /path/to/code/command_files/ubuntu/systemd/helio_gui.service /etc/systemd/system/helio_gui.service
  - ln /path/to/code/command_files/ubuntu/systemd/helio_main.service /etc/systemd/system/helio_main.service
- add a shortcut to the gui in the startup folder or kiosk, Gui address: http://localhost:5173/

### Camera configuration:
Use PTZ for the camera via the web interface to get the entire rack in view. Disable the "Display Date" option in the configuration under Image -> OSD Settings. Set Exposure Mode Manual, Shutter 1/250 (depending on lights), Gain 15 (depending on lights). Change the IP (and default gateway) from the default to an individual IP in the configuration under Network.

### Calibration and configuration:
In the config file at "code/config/-system-.toml" you can edit the IPs of cameras, folder paths, and some code parameters.  
For more info refence the manual: https://docs.google.com/document/d/1zA5PQE_CZmbYyaF8VxmQqYuve3IhWP8sqpmux9wL__U/edit  
Each system has its own config file. You can select the config file you want to use via the "code/config/active.toml" file (see code/config/README.md for more info).


## Running

### Services
The GUI (helio_gui) and the main script (helio_main) both start automatically on startup.  
To stop manually: sudo systemctl stop helio_main  
To start manually: sudo systemctl start helio_main  
To restart manually: sudo systemctl restart helio_main  
To look at service logs: journalctl -b -u helio_main  

### Request Pickpoints
Pickpoints will be requested by the PC Data software.  
To request pickpoints manually, use the server_test.py script, where you can input a requested box_id or input "q" to request all boxes.  
The request will be received by the REST server thread, which will forward it to be processed with the right camera. After the pickpoints are calculated, they will be shown and sent to the communication thread, which will send it to PC Data.  

### Robot Picking
The robot is directed by the PC Data software.  

The following is likely outdated, and someone from PC Data should be asked to test robot picking:  

To make it pick manually, use the MySQL Workbench software and enter the PC Data database (samplerobot). Use the sql scripts in code/scripts/pc_data. Open these in MySQL Workbench and execute in order: delete -> buffer_order -> robot_order.  
In robot_order you can change the productLocation to pick in different boxes. A1 is 1111, A5 is 1115, A25 is 1155, .... You can add multiple values to pick them in order.  
After running the scripts, go to http://localhost:2420/api/pickToRobot/test/scanner/buffer?lpn=123456 to start the robot pick.


## Calibration

### First time calibration
First, empty out all the boxes and make sure you use stable lighting conditions (the same lighting conditions as for a demo/production). Then use control_ptz.py to take an image and place it into calibration/background_images/camera_(id).  
Use LabelMe to accurately determine all the box locations by pinpointing the corners of the visible regions of the bottoms of the boxes. Use the parser in code/scripts to write the data into calibrations/box_locations/camera_(id)/box_locations_0.json. The format should be {'box_id': [[x1, y1], ...], ...}.  
Do this for every camera, then run the camera-box calibration and finally the robot calibration via the GUI in the maintenance tab.

### Camera-Box calibration
If the camera or rack has moved since the first time calibration, you should run the camera-box calibration.  
Via the GUI enter the maintenance and press "Camera-Bak Kalibratie". Follow the on-screen instructions.

### Box-Robot Calibration
If it's the first time calibrating, or if the rack has moved since the first time calibration, you should run the box-robot calibration.  
Via the GUI enter the maintenance and press "Robot Kalibratie". The GUI will instruct you to move the robot manually to a number of markers. To move the robot manually, refer to the 'Robot Controls' section in https://docs.google.com/document/d/18zEm2kazEZOjSmiYSO6DR2Fnbro5Pv2lREMRsvPlfJY/edit#heading=h.272dem89sguu. 
Move the gripper to the markers as per the GUI's instructions and fill in the data on the GUI.


## Documentation

### Specs
A pickpoint should be returned within 3 seconds.  

### Conventions
The origin of each box is bottomright, with x going up in the image and y going left. The 3D height of the origin should be at the bottom of the box.  
The picking area at the bottoms of the boxes are 195mm x 250mm and the boxes are 150mm high.

Box IDs/numbers have two formats:  
In the "vision" format, they are numbers xyzw where x is the robot ID, y is the camera id, z is the row from top to bottom, and x is the column from right to left.  
In the "ici" format, they are numbers 001 to 100. 001 is the box in the top right of the first rack, 005 is the box in the top left of the first rack, 025 is the box in the bottom left of the first rack, 100 is the box in the bottom left of the last rack.  

The racks are at an angle of -20°.  
The camera ID's are [1, 2, 3, 4] in following setup:  
<pre>
      4           3  
  
          Robot  Place_position
  
      1           2  
</pre>
The ordering of corners/markers in row_locations_robot and row_locations_vision must match.  
The ordering of corners/markers in row_locations_robot and row_locations_vision are in order: top-left, bottom-left, top-right, bottom-right.  
The robot coordinates should be in the world coordinates.  
Pickpoint priorities are higher prio for lower numbers: priority 1 is highest priority.

### Process flow
- On startup, all threads and processes are started and wait on new requests.  
- Requests come in from PC Data in the REST server thread.  
- The REST server thread forwards the request to the requests thread.  
- The requests thread attaches the correct camera image to the request and puts it into a sorted list.  
- The picking process takes the first request from the sorted list and analyzes it.  
- The results are sent to the communication thread, which sends it to PC Data.  
- The GUI thread is connected to all the others threads and processes to display their data.  

### Vision documentation
There are three main parts of the vision system: segmentation, background subtraction, and pickpoint scoring:

#### Segmentation

#### Background subtraction
In order to remove the background from the image, we use a template matching algorithm. 
Template images were taken during algorithm finetuning and are used to determine the bottom of the boxes in the images.
HSV color space is also used to make sure most products are not flagged as background. 
The main parameters for the template matching are the template_thresholds. These parameters are linked to the templates in the calibration folder and determine how strict they are matched. Increasing the values will flag less background and allow more background-colored products.

#### Pickpoint scoring
Pickpoint scoring is done based on a number of features of the segmentation masks. The goal is to avoid picking on the background and to avoid picking on the line inbetween products. Ideally, the best pickpoint should be on a product on top.

To achieve this, we use a combination of the following features:
- The certainty of the mask
- The size of the mask
- The rectangularity of the mask
- The background subtraction score

We also split bigger masks into smaller ones to avoid picking inbetween products.

### Coordinate transformations
There are five coordinate reference frames in the system:
- The image reference frame: The coordinate in the full camera image
- The box image reference frame: The coordinate in the transformed camera image of only the box
- The robot world reference frame: The coordinate in the robot's world reference frame
- The robot box world reference frame: The coordinate in the robot's world reference frame but offset with respect to the box
- The robot box reference frame: The coordinate in the robot's box reference frame which starts in the box's origin point and is rotated with respect to the box

The coordinate transformations are done in the coordinate_transformations.py file and are calibrated using the camera-box calibration and the robot calibration.  

The robot always picks perpendicular to the boxes, but the camera looks at a slight angle to the boxes. Because of this, the x, y coordinate sent to PC Data can be up to 40 mm off from the actual x, y coordinate for samples at the top of the boxes in the corners of the rack. For more information on this, see [the slack post](https://heliovision.slack.com/archives/C059ERT5ABD/p1718981270413759) and [the calculation script](https://drive.google.com/file/d/1agBsPUKHN6RYaKnW0Xxzbz3Z9ReZgID1/view?usp=sharing).


### Calibration documentation

#### Camera-box calibration
Markers are found based on color and shape. The lines on the markers are also used as a feature but this is not very strict as the lines are sometimes less visible due to blurring.

#### Robot calibration
During the robot calibration only the x, y and z are calibrated. This is because the rotations are hard to get exact by hand, so we calculate these based on the coordinates over the entire rack. The roll (r) should be parallel with the length of the boxes. The yaw (w) should be the tilt of the rack along its length. The pitch (p) should be the tilt of the boxes along their length.  
The robot is calibrated at the height of the calibration markers. This can be slightly above the actual height of the bottom of the boxes. To account for this and set the origin of the boxes at the correct height, an offset is added when calculating the box origin coordinates.
