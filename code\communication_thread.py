import asyncio
import multiprocessing
import traceback
import time
from ctypes import c_bool

from python_logger import logger
from python_REST_client import RESTClient
from utilities import put_in_bounded_queue, get_timestamp, start_as_thread


class CommunicationThread:
    """
    Represents a thread that sends results from the system to PC Data.
    """

    def __init__(
        self, communication_queue: multiprocessing.Queue, gui_queue: multiprocessing.Queue
    ):
        self.communication_queue = communication_queue
        self.gui_queue = gui_queue
        self.api_client = RESTClient()
        self.heartbeat_client = RESTClient()
        self.result = None
        self.timeout_duration = 10
        self.timeouts = {
            "pickpoints": -self.timeout_duration,
            "position": -self.timeout_duration,
        }
        self.buffer_limit = 600
        self.buffers = {
            "pickpoints": [],
            "position": [],
        }
        self.connected = multiprocessing.Value(c_bool, True)

    async def run(self):
        logger.info("COMMUNICATION THREAD: Start handling.")
        start_as_thread(self.heartbeat_client.heartbeat_loop, (self.gui_queue, self.connected))
        buffering = True
        while True:
            try:
                # Wait for connection
                if not self.connected.value:
                    buffering = True
                    await asyncio.sleep(0.1)
                elif buffering:
                    self.result = {"type": "pickpoints"}
                    self.process_buffer()
                    self.result = {"type": "position"}
                    self.process_buffer()
                    buffering = False

                # Wait for next request
                while self.communication_queue.empty():
                    await asyncio.sleep(0.1)
                self.result = self.communication_queue.get()
                communication_type = self.result["type"]

                # Check if type is timed out
                timestamp = time.perf_counter()
                if timestamp - self.timeouts[communication_type] <= self.timeout_duration:
                    self.add_to_buffer()
                    continue

                # Handle request
                try:
                    await self.handle_result()
                    self.process_buffer()
                except Exception:
                    logger.error(
                        f"COMMUNICATION THREAD: Error for {self.result['type']}, timing out"
                    )
                    logger.error(f"COMMUNICATION THREAD: {traceback.format_exc()}")
                    self.timeouts[communication_type] = timestamp
                    self.add_to_buffer()
                    put_in_bounded_queue(
                        self.gui_queue,
                        {
                            "type": "error_log",
                            "error_message": f"Communication error of type {self.result['type']}!",
                            "timestamp": get_timestamp(format="%Y-%m-%d_%Hh:%Mm:%Ss"),
                        },
                    )

            # Give error if thread crashes
            except Exception:
                logger.error("COMMUNICATION THREAD: ERROR")
                logger.debug(f"COMMUNICATION THREAD: {traceback.format_exc()}")
                put_in_bounded_queue(
                    self.gui_queue,
                    {
                        "type": "error_log",
                        "error_message": "The communication thread encountered an unexpected error!",
                        "timestamp": get_timestamp(format="%Y-%m-%d_%Hh:%Mm:%Ss"),
                    },
                )
                try:
                    while not self.communication_queue.empty():
                        self.communication_queue.get()
                except Exception:
                    continue
                continue

    async def handle_result(self):
        communication_type = self.result["type"]
        if communication_type == "pickpoints":
            box_id = self.result["box_id"]
            pickpoints = self.result["pickpoints"]
            timestamp = self.result["timestamp"]
            await self.api_client.send_pickpoints(box_id, pickpoints, timestamp)
        elif communication_type == "position":
            box_positions = self.result["box_positions"]
            await self.api_client.send_box_positions(box_positions)
        else:
            logger.warning(
                f"COMMUNICATION THREAD: Unknown communication type: {communication_type}"
            )

    def add_to_buffer(self):
        buffer = self.buffers[self.result["type"]]
        if len(buffer) >= self.buffer_limit:
            buffer.pop(0)
        buffer.append(self.result)

    def process_buffer(self):
        buffer = self.buffers[self.result["type"]]
        if len(buffer) == 0:
            return
        logger.info("COMMUNICATION THREAD: Communication restored, processing buffer")
        for result in buffer:
            put_in_bounded_queue(self.communication_queue, result, used_logger=logger)
        buffer.clear()


async def main_test():
    # Start communication thread
    gui_queue = multiprocessing.Queue()
    communication_queue = multiprocessing.Queue()
    communication_thread = CommunicationThread(communication_queue, gui_queue)
    start_as_thread(communication_thread.run)

    # Send pickpoints
    box_id = 1131
    pickpoints = [
        [1, 1],
        [2, 2],
        [3, 3],
    ]
    put_in_bounded_queue(
        communication_queue,
        {
            "type": "pickpoints",
            "box_id": box_id,
            "pickpoints": pickpoints,
            "timestamp": get_timestamp(),
        },
        used_logger=logger,
    )
    await asyncio.Future()


if __name__ == "__main__":
    asyncio.run(main_test())
