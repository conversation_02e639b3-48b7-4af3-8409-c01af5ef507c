<script>
  // Imports
  import { pythonStore } from "../stores";
  export let style = '';

  const { sendCommand } = pythonStore;

  let tableHeaders = ['Nb', 'Status']
  $: tableData = $pythonStore.data['boxStatus'] || [
    ['121', 'Empty'], 
    ['324', 'Empty'], 
    ['434', 'Almost empty'], 
    ['243', 'Almost empty'],
    ['142', 'Almost empty'],
    ['255', 'Almost empty'],
    ['351', 'Full'], 
    ['432', 'Full'], 
    ['133', 'Full'], 
    ['424', 'Full'], 
    ['344', 'Full'], 
    ['245', 'Full'], 
    ['241', 'Full'], 
    ['242', 'Full'], 
    ['211', 'Full'], 
    ['243', 'Full'],  
    ['243', 'Full'], 
    ['243', 'Full'], 
    ['243', 'Full'],  
    ['243', 'Full'], 
    ['243', 'Full'], 
    ['243', 'Full'], 
    ['243', 'Full'], 
  ];

  function isRed(status) {
    if (status === 'Empty') {
      return true
    }
    return false
  }

  function isOrange(status) {
    if (status === 'Almost empty') {
      return true
    }
    return false
  }

  function isGrey(status) {
    if (status === 'Unknown') {
      return true
    }
    return false
  }
  
  function refillBox(boxId) {
    sendCommand("refill_box", [boxId,]);
  }
</script>

<style>
  table_container {
    display: block;
    height: 26.4em !important; /* Adjust as necessary */
    width: 14.3em; /* Adjust as necessary */
    overflow-y: scroll; /* Makes the container scrollable */
    border-style: solid;
    border-color: #55BA2B;
    border-radius: 4px;
    padding: 0px;
  }

  table {
    width: 100%;
    height: 100%;
    border-collapse: separate;
  }

  table, th, td {
    border: 1px solid black;
    border-spacing: 0px;
    border-collapse: separate; 
    box-sizing: border-box !important;
    table-layout: auto !important;
  }

  thead th {
    position: sticky;
    top: 0px; /* Adjust if you have any margin or padding at the top */
    background: white; /* Ensures the header background isn't transparent */
    z-index: 10; /* Ensures the header overlaps the content when scrolling */
  }

  th, td {
    text-align: center;
    justify-content: center;
    border: 1px solid black;
    padding: 0.3em;
    overflow: hidden; /* Prevent content from overflowing cells */
    box-sizing: border-box;
    text-overflow: ellipsis; /* Add an ellipsis to overflowing content */
    white-space: nowrap; /*Keep content on a single line */
    min-width: 2%;
    max-width: 20%;
    width: 15%; /* Adjust the percentage as needed */
    font-size: 12px;
  }

  button {
    font-size: 12px;
    width: 6.1em;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    min-height: 28px;
    border: 2px solid #55BA2B;
    border-radius: 1.7em;
    color: #212121;
  }

  centerDiv {
    display: flex;
    justify-content: space-evenly;
  }

  table_container tr:nth-child(odd) {
    background-color: #f2f2f2; /* Light grey for odd rows */
  }

  table_container tr:nth-child(even) {
    background-color: white; /* White or another color for even rows */
  }

  .red {
    background-color: #FF9B9B !important; /* Use !important to override other background styles */
  }

  .orange {
    background-color: #EFC19E !important; /* Use !important to override other background styles */
  }

  .grey {
    background-color: lightgrey !important; /* Use !important to override other background styles */
  }
</style>

<table_container style='{style}'>
  <table>
    <thead>
      {#each tableHeaders as header}
        <th>
          {header}
        </th>
      {/each}
      <th>
        {'Refill'}
      </th>
    </thead>
    <tbody>
      {#each tableData as log}
        <tr class:red={isRed(log[1])} class:orange={isOrange(log[1])} class:grey={isGrey(log[1])}>
          {#each log as cell}
            <td>
              {cell}
            </td>
          {/each}
          {#if isRed(log[1]) || isOrange(log[1])}
            <td>
              <centerDiv>
                <button 
                  on:click={refillBox(log[0])}>
                  Box refilled
                </button>
              </centerDiv>
            </td>
          {:else}
            <td/>
          {/if}
        </tr>
      {/each}
    </tbody>
  </table>
</table_container>
  