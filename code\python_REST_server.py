import uvicorn
from fastapi import FastAP<PERSON>, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel, <PERSON>
from typing import List
import traceback

from utilities import put_in_bounded_queue, get_timestamp
from python_logger import logger
from config_loader import config


class PickpointRequest(BaseModel):
    boxId: int = Field(..., gt=0, description="The ID of the box.")
    priority: int = Field(
        ..., gt=0, le=10, description="The priority of the calculation of the pick points."
    )


class RESTServer:
    """
    Holds and handles API data.
    """

    def __init__(self):
        self.robot_id = str(config.get_setting("other", "robot_id"))
        self.picking_queue = None  # Updated via main directly
        self.logger = logger

    def run_api_server(self):
        self.logger.debug("Running api server")
        uvicorn.run(app, host="localhost", port=8009, log_level="warning")

    def request_pickpoints(self, requests: List[PickpointRequest]):
        self.logger.debug("REST Server: Received pickpoint request.")
        request_timestamp = get_timestamp()
        try:
            requests_sorted = sorted(requests, key=lambda x: x.priority)
            for requested in requests_sorted:
                box_id = requested.boxId
                if len(str(box_id)) != 4:
                    self.logger.debug(
                        f"REST Server: Invalid box id: {box_id}. Box ID should be of length 4."
                    )
                    return JSONResponse(
                        status_code=400,
                        content={
                            "message": f"Invalid box id: {box_id}. Box ID should be of length 4."
                        },
                    )
                robot_id = str(box_id)[0]
                if robot_id != self.robot_id:
                    self.logger.debug(
                        f"REST Server: Requested box at robot {robot_id}, but this is robot {self.robot_id}."
                    )
                    return JSONResponse(
                        status_code=400,
                        content={
                            "message": f"Requested box at robot {robot_id}, but this is robot {self.robot_id}."
                        },
                    )
                camera_id = int(str(box_id)[1])
                if camera_id not in config.get_setting("other", "camera_ids"):
                    self.logger.debug(f"REST Server: Camera ID {camera_id} not found.")
                    return JSONResponse(
                        status_code=404,
                        content={"message": f"Camera ID {camera_id} not found."},
                    )
                box_number = str(box_id)[2:]
                for digit in box_number:
                    if int(digit) <= 0 or int(digit) >= 6:
                        self.logger.debug(f"REST Server: Box ID {box_id} not found.")
                        return JSONResponse(
                            status_code=404,
                            content={"message": f"Box ID {box_id} not found."},
                        )
                priority = requested.priority
                put_in_bounded_queue(
                    self.picking_queue,
                    {"box_id": box_id, "priority": priority, "timestamp": request_timestamp},
                )
            return {"message": "Request has been received and will be processed."}
        except Exception:
            self.logger.debug(
                f"REST Server: Encountered unexpected error: {traceback.format_exc()}."
            )
            return JSONResponse(
                status_code=500,
                content={"message": f"Encountered unexpected error: {traceback.format_exc()}."},
            )


app = FastAPI()
api_server = RESTServer()


@app.post("/box/request_pick_points", status_code=status.HTTP_200_OK)
def request_pickpoints(requests: List[PickpointRequest]):
    return api_server.request_pickpoints(requests)


if __name__ == "__main__":
    api_server.run_api_server()
