import sys
import os

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from utilities import load_json
from calibration import save_box_locations


def parse_box_locations(file):
    # Load data
    label_json = load_json(file)
    regions = label_json["shapes"]

    # Format data
    box_locations = {}
    for region in regions:
        box_locations[region["label"]] = region["points"]
    return box_locations


if __name__ == "__main__":
    # Select file
    labelme_file = r"C:\Users\<USER>\Desktop\2024-09-02_13-21-47-018410.json"
    camera_id = "1"

    # Parse and save
    box_locations = parse_box_locations(labelme_file)
    save_box_locations(box_locations, camera_id)

    # Finish
    print("Done")
