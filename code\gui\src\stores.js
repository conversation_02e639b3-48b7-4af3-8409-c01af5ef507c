import { pythonStore as makePythonStore } from "@heliovision/svelte-python-store";
import { writable } from "svelte/store";
import { websocketConfig } from "./config";

// Web Socket Store (Python)
const { host, port, debug } = websocketConfig;
export const pythonStore = makePythonStore(
  `${host}:${port}`,
  {},
  [],
  100,
  true,
  !!debug
);

// Svelte App Data Store
export const svelteStore = writable({});
