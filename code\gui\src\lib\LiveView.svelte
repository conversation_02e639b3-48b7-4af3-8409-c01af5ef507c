<script>
  // Imports
  import { Card } from "@heliovision/sveltestrap";
  import { Row, Col } from "sveltestrap";
  import { pythonStore } from "../stores";
  import ErrorLogTable from "./ErrorLogTable.svelte";
  import BoxStatusTable from "./BoxStatusTable.svelte";
  
  export let index;

  $: lastPickpointImage = $pythonStore?.live_images?.last_pickpoint || 'shown/placeholder.png';
  $: lastTimestamp = $pythonStore?.data['last_timestamp'] || '2024-02-16_11h:51m:23s';
  $: lastBoxId = $pythonStore?.data['last_box_id'] || '2341';
  
  function maintenanceTab() {
    index = 1;
  }
</script>

<style>
  button {
    font-size: 0.9em;
    width: 7em;
    height: 2.5em;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    padding: 0 20px;
    min-height: 35px;
    border: 2px solid #55BA2B;
    border-radius: 20px;
    background: #55BA2B;
    color: white;
  }
</style>

<Card title="" color="primary-subtle" style='margin-top: 0px; padding-top: 0px; padding-bottom: 0px;'>
  <Col>
    <Row style='margin: 0px; margin-left: -10px; margin-right: -14px; padding: 0px;'>
      <h6 style="margin: 10px; margin-bottom: 3px; margin-top: 0px; font-size: 16px;">Error Logs</h6>
      <ErrorLogTable style="margin-left: 20px; height: 8.2em !important; width: 94%;"></ErrorLogTable>
      <div style='padding: 6px'></div>
    </Row>
    <Row style='margin: 0px; margin-left: -10px; margin-right: -10px; padding: 0px; display:flex; min-width: 28em'>
      <Col style='margin: 0px; padding: 0px;'>
        <div style='margin-left: 28px; display: flex; align-items: end;'>
          <h6 style="margin: 0px; padding: 0px; margin-right: 5px; font-size: 16px; text-wrap: wrap;">Last Pickpoint: </h6>
          <h6 style="margin: 0px; padding: 0px; padding-bottom: 1px; font-size: 14px; text-wrap: wrap; font-weight: bold;">{lastTimestamp}</h6>
        </div>
        <div style='margin-left: 28px; display: flex; align-items: end;'>
          <h6 style="margin: 0px; padding: 0px; margin-right: 5px; font-size: 16px; text-wrap: wrap;">Boxnumber: </h6>
          <h6 style="margin: 0px; padding: 0px; padding-bottom: 1px; font-size: 14px; text-wrap: wrap; font-weight: bold;">{lastBoxId}</h6>
        </div>
        <div style='padding: 3px'></div>
        {#if lastPickpointImage}
          <img src={lastPickpointImage} alt="live" style="margin-left: 20px; width: 100% !important; max-width: 23em"/>
        {/if}
      </Col>
      <Col style='margin: 0px; padding: 0px; margin-right: 20px; display: flex; justify-content: flex-end; max-width: 40%; flex-direction: column; align-items: flex-end;'>
        <Col style='margin: 0px; padding: 0px; display: flex; justify-content: flex-end;'>
          <Row style='margin: 0px; padding: 0px; max-width: 15.5em; align-items: flex-end; flex-direction: column;'>
            <h6 style="margin: 10px; margin-left: -4px; margin-bottom: 3px; margin-top: 0px; font-size: 16px">Box Statuses</h6>
            <BoxStatusTable></BoxStatusTable>
          </Row>
        </Col>
        <Col style='margin: 0px; padding: 0px; display: flex; justify-content: flex-end;'>
          <Row style='margin: 0px; padding: 0px;'>
            <Col style="margin: 0px; padding: 0px; padding-top: 20px; display: flex; align-items: flex-end; flex-direction: column;">
              <button 
                on:click={maintenanceTab}
                style="width: 13em; padding: 5px;">
                Maintenance and Options
              </button>
            </Col>
          </Row>
        </Col>
      </Col>
    </Row>
  </Col>
</Card>
