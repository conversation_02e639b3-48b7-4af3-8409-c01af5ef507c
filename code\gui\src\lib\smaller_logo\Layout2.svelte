<script>
  // Imports
  import Header2 from "./Header2.svelte";
  import { Container } from "sveltestrap";

  // Arguments
  export let logo = undefined;
  export let project = undefined;
  let className = "";
  export { className as class };

  $: classes = `${className} d-flex flex-column mt-5`;
</script>

<div class="min-vh-100">
  <Header2 {logo} {project} />

  <slot name="not-main" />

  <main>
    <Container class={classes} xxl {...$$restProps}>
      <slot />
    </Container>
  </main>
</div>
  