import cv2 as cv
import numpy as np
import os

from config_loader import config
from utilities import get_timestamp, load_json, write_json, get_latest_file_or_0
from python_logger import logger
from control_ptz import get_single_image
from marker_detection import detect_markers, validate_markers, format_markers
from coordinate_transformations import get_box_origin_robot, get_rotations


async def camera_to_box_calibration(
    camera_id, given_image=None, visualize=False, output_image=None
):
    """
    Calibrate the position of the boxes for a camera.
    The calibration starts with an initial calibration of the position of the boxes and initial positions of the calibration markers.
    The calibration then transforms the position of the boxes based on the new positions of the calibration markers.
    """
    logger.info("CALIBRATION: Starting box calibration.")

    # Get image
    if given_image is None:
        image = await get_single_image(camera_id)
    else:
        image = given_image
    saving_status = load_json(config.get_setting("folder_paths", "adjustable_settings_path"))[
        "image_saving_status"
    ]
    if saving_status:
        # Save the calibration image
        directory = config.get_setting("folder_paths", "storage_folder") + "/calibration/"
        if not os.path.exists(directory):
            os.makedirs(directory)
        image_file_name = f"{get_timestamp()}_camera_{camera_id}.png"
        logger.debug(f"CALIBRATION: Saving calibration image: {image_file_name}")
        cv.imwrite(f"{directory}{image_file_name}", image)

    # Detect markers
    detected_markers = detect_markers(image, visualize, output_image=output_image)
    if not validate_markers(detected_markers):
        logger.error(f"CALIBRATION: Markers invalid for camera {camera_id}!: {detected_markers}")
        return False
    detected_row_locations = format_markers(detected_markers)
    detected_markers = [marker for row in detected_row_locations.values() for marker in row]
    logger.info(f"CALIBRATION: Markers: {detected_markers}")

    # Get transformation
    row_locations_vision_root = (
        f"{config.get_setting('folder_paths', 'calibration_folder')}/row_locations_vision"
    )
    original_row_locations = load_json(
        f"{row_locations_vision_root}/camera_{camera_id}/row_locations_vision_0.json"
    )
    original_markers = [marker for row in original_row_locations.values() for marker in row]
    transformation_matrix = get_transformation_matrix(original_markers, detected_markers)
    logger.debug(f"CALIBRATION: Tranformation matrix: {transformation_matrix}")

    # Apply box location transformations
    box_locations_root = (
        f"{config.get_setting('folder_paths', 'calibration_folder')}/box_locations"
    )
    box_locations = load_json(f"{box_locations_root}/camera_{camera_id}/box_locations_0.json")
    new_box_locations = transform_box_locations(box_locations, transformation_matrix)
    logger.debug(f"CALIBRATION: new_box_locations: {new_box_locations}")

    # Save all results
    save_row_locations_vision(detected_markers, camera_id)
    save_box_locations(new_box_locations, camera_id)

    logger.info("CALIBRATION: Box calibration finished successfully.")
    return True


def get_transformation_matrix(original_marker_positions, new_marker_positions):
    logger.debug(original_marker_positions)
    logger.debug(new_marker_positions)
    transformation_matrix, _ = cv.findHomography(
        srcPoints=np.asarray(original_marker_positions),
        dstPoints=np.asarray(new_marker_positions),
        method=cv.RANSAC,
        ransacReprojThreshold=5.0,
    )
    return transformation_matrix


def transform_box_locations(box_locations, transformation_matrix):
    for box in box_locations.keys():
        corners = box_locations[box]
        corners = cv.perspectiveTransform(
            np.array([corners], dtype="float32"), transformation_matrix
        )[0]
        corners = [[int(corner[0]), int(max(0, min(corner[1], 1440)))] for corner in corners]
        box_locations[box] = corners
    return box_locations


def save_row_locations_vision(marker_list, camera_id):
    # Format markers to row locations
    row_locations = format_markers(marker_list)

    # Save row locations
    timestamp = get_timestamp()
    row_locations_vision_root = (
        f"{config.get_setting('folder_paths', 'calibration_folder')}/row_locations_vision"
    )
    new_config_path = (
        f"{row_locations_vision_root}/camera_{camera_id}/row_locations_vision_{timestamp}.json"
    )
    write_json(new_config_path, row_locations)


def save_box_locations(box_locations, camera_id):
    timestamp = get_timestamp()
    box_locations_root = (
        f"{config.get_setting('folder_paths', 'calibration_folder')}/box_locations"
    )
    new_config_path = f"{box_locations_root}/camera_{camera_id}/box_locations_{timestamp}.json"
    write_json(new_config_path, box_locations)


def save_row_locations_robot(row_locations_robot, camera_id):
    """
    Save the new robot coordinates from the GUI calibration.
    """
    timestamp = get_timestamp()
    row_locations_robot_root = (
        f"{config.get_setting('folder_paths', 'calibration_folder')}/row_locations_robot"
    )
    new_config_path = (
        f"{row_locations_robot_root}/camera_{camera_id}/row_locations_robot_{timestamp}.json"
    )
    write_json(new_config_path, row_locations_robot)


def get_box_positions(camera_id, given_row_locations_robot=None):
    # Get data
    box_locations_root = (
        f"{config.get_setting('folder_paths', 'calibration_folder')}/box_locations"
    )
    box_locations = load_json(f"{box_locations_root}/camera_{camera_id}/box_locations_0.json")
    if given_row_locations_robot is None:
        row_locations_robot_root = (
            f"{config.get_setting('folder_paths', 'calibration_folder')}/row_locations_robot"
        )
        row_locations_robot = load_json(
            get_latest_file_or_0(f"{row_locations_robot_root}/camera_{camera_id}")
        )
    else:
        row_locations_robot = given_row_locations_robot

    # Get box coordinates
    rotations = get_rotations(camera_id, given_row_locations_robot=row_locations_robot)
    box_positions = []
    for box_id in box_locations.keys():
        box_origin = get_box_origin_robot(
            box_id, camera_id, given_row_locations_robot=row_locations_robot
        )
        box_positions += [
            {
                "boxId": box_id,
                "referenceFrame": {
                    "x": box_origin[0],
                    "y": box_origin[1],
                    "z": box_origin[2],
                    "rx": rotations[0],
                    "ry": rotations[1],
                    "rz": rotations[2],
                },
            },
        ]
    return box_positions


if __name__ == "__main__":
    import asyncio

    # Set image
    camera_id = 4
    background_image_folder = f"{config.get_setting('folder_paths', 'calibration_folder')}/background_images/camera_{camera_id}"
    background_image_path = get_latest_file_or_0(background_image_folder)
    image = cv.imread(background_image_path)
    # image = cv.imread(r'data/images/test/calibration testing/2024-08-26_15-23-47-948901_camera_2.png')

    # Camera to box calibration
    asyncio.run(camera_to_box_calibration(camera_id, image, visualize=True))

    # # Camera to robot calibration
    # TODO example

    # Get box positions
    logger.info(get_box_positions(camera_id))
