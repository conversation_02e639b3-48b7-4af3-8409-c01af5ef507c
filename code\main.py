import asyncio
import cv2 as cv
import os
import multiprocessing
import traceback
from typing import List
import numpy as np

from heliovision.disk_cleaner import Disk<PERSON>ull<PERSON>leaner
from config_loader import config
from python_logger import logger, logger_prep
from utilities import (
    get_timestamp,
    start_as_task,
    start_as_thread,
    start_as_process,
    put_in_bounded_queue,
    datetime_string_to_day,
    load_json,
)
from coordinate_transformations import transform_box_pickpoint_image_to_robot
from image_transformations import transform_and_crop_to_box, create_background_mask
from visualisations import segment_background_image, segment_pickpoints_image
from python_gui_commands import PythonGUICommands
from pickpoint_segmenter import PickpointSegmenter
from python_REST_server import api_server
from gui_thread import GUIThread
from communication_thread import CommunicationThread
from requests_thread import RequestsThread


class PickingProcess:
    """
    Represents a process that analyzes requests from the RequestsThread to find pickpoints.
    """

    def __init__(
        self,
        camera_ids: List[int],
        gui_queue: multiprocessing.Queue,
        communication_queue: multiprocessing.Queue,
        picking_queue: multiprocessing.Queue,
        cameras_calibrated_queue: multiprocessing.Queue,
    ):
        self.camera_ids = camera_ids
        self.gui_queue = gui_queue
        self.communication_queue = communication_queue
        self.cameras_calibrated_queue = cameras_calibrated_queue
        self.pickpoint_segmenter = None
        self.requests_thread = RequestsThread(
            self.camera_ids,
            self.gui_queue,
            self.communication_queue,
            picking_queue,
            self.cameras_calibrated_queue,
        )
        self.safety_margin = config.get_setting("pickpoint_configuration", "safety_margin")
        if self.safety_margin < 6:
            self.safety_margin = 6
        self.empty_threshold = config.get_setting("pickpoint_configuration", "empty_threshold")
        if self.empty_threshold < 0:
            self.empty_threshold = 0
        self.almost_empty_threshold = config.get_setting(
            "pickpoint_configuration", "almost_empty_threshold"
        )
        self.cameras_calibrated = False

    async def run(self):
        """
        Checks the designated camera's frames for defects.
        """
        # Start the requests thread
        await self.requests_thread.run()
        logger.info("PICKING PROCESS: Image and request loops started.")

        # Initiate the network
        self.pickpoint_segmenter = PickpointSegmenter()
        logger.info("PICKING PROCESS: Neural network initiated.")

        # Start processing
        start_as_task(self._update_calibration_status)
        logger.debug("PICKING PROCESS: Processing")
        frame_counter = 0
        while True:
            # Wait for next request
            box_id, image = await self.requests_thread.get_next_request()
            camera_id = int(str(box_id)[1])
            logger.debug(f"PICKING PROCESS: New request: {box_id}")
            frame_counter += 1

            # Don't analyze uncalibrated images
            if not self.cameras_calibrated:
                logger.warning("PICKING PROCESS: Cameras are not calibrated. Skipping analysis.")
                put_in_bounded_queue(
                    self.gui_queue,
                    {
                        "type": "modal",
                        "title": "Cameras not calibrated",
                        "message": "Cameras need to be recalibrated with box calibration!",
                    },
                    used_logger=logger,
                )
                continue

            # Start pickpoint analysis
            str_date_time_analysis = get_timestamp()
            try:
                # Crop to box
                box_image = transform_and_crop_to_box(image, box_id, camera_id)
                box_image_width, box_image_height, colors = box_image.shape

                # Determine background
                mask_full = create_background_mask(image)
                box_background_mask = transform_and_crop_to_box(mask_full, box_id, camera_id)

                # Select pickpoints
                pickpoint_yielder = self.pickpoint_segmenter.get_pickpoints(
                    box_image, background_image=box_background_mask, verbose=True
                )

                # Filter pickpoins
                pickpoints = []
                segmentations = []
                robot_pickpoints = []
                for pickpoint, segmentation in pickpoint_yielder:
                    if (
                        (pickpoint[0] < self.safety_margin)
                        or (pickpoint[0] > box_image_height - self.safety_margin)
                        or (pickpoint[1] < self.safety_margin)
                        or (pickpoint[1] > box_image_width - self.safety_margin)
                    ):
                        logger.debug(f"PICKING PROCESS: Too close to edge: {pickpoint}")
                        continue
                    if pickpoint[1] < 30 and str(box_id)[2] != "1":
                        logger.debug(f"PICKING PROCESS: Too close to overhang: {pickpoint}")
                        continue
                    robot_pickpoint = transform_box_pickpoint_image_to_robot(
                        pickpoint, box_id, camera_id
                    )
                    if (robot_pickpoint[0] < 0) or (robot_pickpoint[1] < 0):
                        logger.error(
                            f"PICKING PROCESS: Pickpoint out of bounds!: {robot_pickpoint}"
                        )
                        continue
                    pickpoints.append(pickpoint)
                    segmentations.append(segmentation)
                    robot_pickpoints.append(robot_pickpoint)
                    if len(pickpoints) >= 10:
                        break

                # Handle empty or almost empty boxes
                background_percentage = np.count_nonzero(box_background_mask) / (
                    box_image_width * box_image_height
                )
                if len(pickpoints) < self.empty_threshold:
                    logger.info(
                        "PICKING PROCESS: Not enough valid pickpoints found! Check if the box is empty."
                    )
                    put_in_bounded_queue(
                        self.gui_queue,
                        {"type": "box_status", "box_id": box_id, "box_status": "Empty"},
                        used_logger=logger,
                    )
                    robot_pickpoints = []
                elif background_percentage >= self.almost_empty_threshold:
                    put_in_bounded_queue(
                        self.gui_queue,
                        {
                            "type": "box_status",
                            "box_id": box_id,
                            "box_status": "Almost empty",
                        },
                        used_logger=logger,
                    )
                else:
                    put_in_bounded_queue(
                        self.gui_queue,
                        {"type": "box_status", "box_id": box_id, "box_status": "Full"},
                        used_logger=logger,
                    )

                # Send pickpoints
                put_in_bounded_queue(
                    self.communication_queue,
                    {
                        "type": "pickpoints",
                        "box_id": box_id,
                        "pickpoints": robot_pickpoints,
                        "timestamp": str_date_time_analysis,
                    },
                    used_logger=logger,
                )

                # Show live image
                if frame_counter % 1 == 0:
                    visualisation_image = segment_background_image(box_image)
                    if len(pickpoints) > 0:
                        visualisation_image = segment_pickpoints_image(
                            pickpoints,
                            segmentations,
                            box_image,
                            max_amount_of_pickpoints_to_draw=config.get_setting(
                                "other", "max_amount_of_pickpoints_to_draw"
                            ),
                        )
                    put_in_bounded_queue(
                        self.gui_queue,
                        {
                            "type": "image",
                            "image": visualisation_image,
                            "box_id": box_id,
                            "timestamp": get_timestamp(format="%Y-%m-%d_%Hh:%Mm:%Ss"),
                        },
                        used_logger=logger,
                    )
                    self.save_results(
                        f"{box_id}_pick",
                        visualisation_image,
                        str_date_time_analysis,
                        robot_pickpoints,
                    )

                # Save results
                self.save_results(box_id, image, str_date_time_analysis, robot_pickpoints)

            # Give error if something goes wrong
            except Exception:
                if box_id is None:
                    box_id = 0
                if camera_id is None:
                    camera_id = 0
                logger.error("PICKING PROCESS: Unexpected picking error.")
                logger.error(f"PICKING PROCESS: {traceback.format_exc()}")
                put_in_bounded_queue(
                    self.gui_queue,
                    {
                        "type": "error_log",
                        "error_message": f"The picking process for camera {camera_id} on box {box_id} failed unexpectedly!",
                        "timestamp": get_timestamp(format="%Y-%m-%d_%Hh:%Mm:%Ss"),
                    },
                    used_logger=logger,
                )
                continue

    def save_results(self, box_id, image, str_date_time, pickpoints):
        saving_status = load_json(config.get_setting("folder_paths", "adjustable_settings_path"))[
            "image_saving_status"
        ]
        if not saving_status:
            return

        # Save the analyzed image
        directory_root = (
            config.get_setting("folder_paths", "storage_folder")
            + "/live_picks/"
            + datetime_string_to_day(str_date_time)
            + "/"
        )
        camera_id = str(box_id)[1]
        subdirectory = str(camera_id) + "/"
        directory = directory_root + subdirectory
        if not os.path.exists(directory):
            os.makedirs(directory)
        image_file_name = f"{str_date_time}_box_{box_id}.png"
        logger.debug(f"PICKING PROCESS: Saving picking image: {image_file_name}")
        logger.debug(f"PICKING PROCESS: pickpoints: {pickpoints}")
        cv.imwrite(f"{directory}{image_file_name}", image)

    async def _update_calibration_status(self):
        while True:
            while self.cameras_calibrated_queue.empty():
                await asyncio.sleep(0.2)
            self.cameras_calibrated = bool(self.cameras_calibrated_queue.get())


async def main():
    """
    Main script: start processes for selecting and handling pickpoints.
    """
    logger_prep(logger)
    logger.info("MAIN: Logger Started.")

    # Start Disk Cleaner
    storage_folder = config.get_setting("folder_paths", "storage_folder")
    if not os.path.exists(storage_folder):
        os.makedirs(storage_folder)
    cleaner = DiskFullCleaner()
    cleaner.add_file_pattern(f"{storage_folder}/live_picks/*/*.png")
    cleaner.start()
    logger.info("MAIN: Disk Cleaner Started.")

    # Initialize variables
    gui_queue = multiprocessing.Queue()
    communication_queue = multiprocessing.Queue()
    picking_queue = multiprocessing.Queue()
    cameras_calibrated_queue = multiprocessing.Queue()

    # Start camera picking process
    camera_ids = config.get_setting("other", "camera_ids")
    picking_process = PickingProcess(
        camera_ids,
        gui_queue,
        communication_queue,
        picking_queue,
        cameras_calibrated_queue,
    )
    start_as_process(picking_process.run)
    logger.info("MAIN: Picking Process Started.")

    # Start GUI
    python_svelte = PythonGUICommands(
        communication_queue=communication_queue,
        picking_queue=picking_queue,
        cameras_calibrated_queue=cameras_calibrated_queue,
        initial_store={"state": "stopped"},
    )
    gui_thread = GUIThread(gui_queue, python_svelte)
    api_server.picking_queue = picking_queue
    start_as_thread(api_server.run_api_server)
    communication_thread = CommunicationThread(communication_queue, gui_queue)
    logger.info("MAIN: Starting GUI and API Threads.")
    await asyncio.gather(python_svelte.start(), gui_thread.run(), communication_thread.run())


if __name__ == "__main__":
    asyncio.run(main())
