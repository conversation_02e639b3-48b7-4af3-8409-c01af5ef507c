<script>
  import { Navbar, <PERSON>v<PERSON><PERSON><PERSON>, Container } from "sveltestrap";
  import { LogoHeliovision, LogoBotko } from "@heliovision/sveltestrap";

  export let logo = "heliovision";
  export let project = "";
</script>

<header>
  <Navbar color="dark" dark class="py-3" {...$$restProps}>
    <Container
      class="d-flex align-items-center justify-content-between mx-3"
      fluid
    >
      <NavbarBrand href={null}>
        {#if typeof logo === "string"}
          {#if logo === "heliovision"}
            <LogoHeliovision style="height: 35px; margin-top: 10px;" />
          {:else if logo === "botko"}
            <LogoBotko style="height: 65px;" />
          {:else}
            <div
              class="fs-2 fw-bold text-uppercase d-flex flex-row align-items-center"
              style="height: 65px;"
            >
              {logo}
            </div>
          {/if}
        {:else}
          <svelte:component this={logo} style="height: 65px;" />
        {/if}
      </Navbar<PERSON>rand>
      {#if project}
        <NavbarBrand href={null} class="fs-2 fw-bold text-uppercase">
          {project}
        </NavbarBrand>
      {/if}
    </Container>
  </Navbar>
</header>
  