import asyncio
import multiprocessing
import traceback

from python_logger import logger
from python_gui_commands import PythonGUICommands, vision_id_to_ici_id


class GUIThread:
    """
    Represents a thread that writes results from the system to the GUI.
    """

    def __init__(self, gui_queue: multiprocessing.Queue, svelte: PythonGUICommands):
        self.gui_queue = gui_queue
        self.svelte = svelte

    async def run(self):
        logger.info("GUI THREAD: Start updating.")
        while True:
            try:
                if self.gui_queue.empty():
                    await asyncio.sleep(0.05)
                    continue
                result = self.gui_queue.get()
                type = result["type"]
                if type == "image":
                    image = result["image"]
                    await self.svelte.update_pickpoint_image(image)
                    timestamp = result["timestamp"]
                    await self.svelte.update_data({"last_timestamp": timestamp})
                    box_id = vision_id_to_ici_id(result["box_id"])
                    await self.svelte.update_data({"last_box_id": box_id})
                elif type == "error_log":
                    error_message = result["error_message"]
                    timestamp = result["timestamp"]
                    await self.svelte.add_error_log(error_message, timestamp)
                elif type == "modal":
                    title = result["title"]
                    message = result["message"]
                    await self.svelte.force_close_modal()
                    await self.svelte.show_info_modal(title, message)
                elif type == "box_status":
                    box_id = vision_id_to_ici_id(result["box_id"])
                    box_status = result["box_status"]
                    await self.svelte.update_box_status(box_id, box_status)
                else:
                    logger.warning("GUI THREAD: Invalid result type")
            except Exception:
                logger.error("GUI THREAD: Unexpected error!")
                logger.debug(f"GUI THREAD: {traceback.format_exc()}")
                continue


async def main_test_runner(gui_queue):
    from utilities import put_in_bounded_queue, get_timestamp

    # Wait for gui to start
    await asyncio.sleep(2)

    # Send messages
    box_id = 1131
    camera_id = 1
    put_in_bounded_queue(
        gui_queue,
        {"type": "box_status", "box_id": box_id, "box_status": "Empty"},
        used_logger=logger,
    )
    put_in_bounded_queue(
        gui_queue,
        {
            "type": "error_log",
            "error_message": f"The camera connection for camera {camera_id} failed!",
            "timestamp": get_timestamp(format="%Y-%m-%d_%Hh:%Mm:%Ss"),
        },
        used_logger=logger,
    )

    # Leave open
    await asyncio.Future()


async def main_test():
    # Start gui threads
    gui_queue = multiprocessing.Queue()
    communication_queue = multiprocessing.Queue()
    python_svelte = PythonGUICommands(
        communication_queue=communication_queue, initial_store={"state": "stopped"}
    )
    gui_thread = GUIThread(gui_queue, python_svelte)
    await asyncio.gather(
        python_svelte.start(), gui_thread.run(), main_test_runner(gui_queue=gui_queue)
    )


if __name__ == "__main__":
    asyncio.run(main_test())
