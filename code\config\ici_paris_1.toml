
[camera_1]
id = "1"
name = "camera_1"
ip = "************"
username = "admin"
password = "b9FKsrrM"

[camera_2]
id = "2"
name = "camera_2"
ip = "************"
username = "admin"
password = "b9FKsrrM"

[camera_3]
id = "3"
name = "camera_3"
ip = "************"
username = "admin"
password = "b9FKsrrM"

[camera_4]
id = "4"
name = "camera_4"
ip = "************"
username = "admin"
password = "b9FKsrrM"

[folder_paths]
storage_folder = "data/images"
calibration_folder = "code/config/calibration/calibration_ici_paris_1"
adjustable_settings_path = "code/config/adjustable_settings/adjustable_settings.json"

[pickpoint_configuration]
safety_margin = 30
empty_threshold = 1
almost_empty_threshold = 0.1

[code_parameters]
background_difference_threshold = 40
background_saturation_threshold = 40
marker_brightness_factor = 1.2
template_thresholds = [0.6, 0.4, 0.4, 0.35, 0.6, 0.4, 0.4, 0.45]

[other]
robot_ip = "***********"
ipc_ip = "*************"
calibration_password = "5497"
camera_ids = [1, 2, 3, 4]
robot_id = 1
camera_delay = 0
max_amount_of_pickpoints_to_draw = 10
