import cv2 as cv
import numpy as np

from config_loader import config
from utilities import load_json, get_latest_file_or_0


def transform_box_pickpoint_image_to_robot(point, box_id, camera_id):
    """
    Transform a point from 2D pixel coordinates in the box image to 2D coordinates in the robot box reference plane
    """
    image_point = transform_coordinate_box_to_image(point, box_id, camera_id)
    robot_point = transform_coordinate_image_to_robot(image_point, box_id, camera_id)
    box_point = transform_coordinate_robot_to_box(robot_point, box_id, camera_id)
    box_frame_point = transform_coordinate_box_to_box_frame(box_point, camera_id)
    return box_frame_point


def transform_coordinate_box_to_image(point, box_id, camera_id):
    """
    Transform a point from 2D pixel coordinates in the box image to 2D pixel coordinates in the full image
    """
    region = get_region(box_id, camera_id)
    return transform_coordinate_region_to_image(point, region)


def transform_coordinate_region_to_image(point, region):
    """
    Transform a point from 2D pixel coordinates in the region to 2D pixel coordinates in the full image
    """
    # Get the inverse transformation matrix
    image_size = (200, 240)  # size of the transformed image
    image_size_x = image_size[0]
    image_size_y = image_size[1]
    new_corner_points = np.array(
        [[0, 0], [image_size_x, 0], [0, image_size_y], [image_size_x, image_size_y]],
        dtype=np.float32,
    )
    inverse_transformation_matrix = cv.getPerspectiveTransform(
        np.array(new_corner_points, dtype=np.float32),
        np.array(region, dtype=np.float32),
    )

    # Perform the inverse mapping
    point_h = np.array(
        [point[0], point[1], 1]
    )  # Add a third coordinate (for homogenous coordinates)
    transformed_point_h = np.dot(inverse_transformation_matrix, point_h)
    transformed_point = (
        transformed_point_h[0] / transformed_point_h[2],
        transformed_point_h[1] / transformed_point_h[2],
    )  # Convert back from homogenous coordinates

    return transformed_point


def transform_coordinate_image_to_robot(point, box_id, camera_id, given_row_locations_robot=None):
    """
    Transform a point from 2D pixel coordinates in the image to 3D coordinates in the robot world reference frame
    """
    # Load corner data
    row_locations_vision_root = (
        f"{config.get_setting('folder_paths', 'calibration_folder')}/row_locations_vision"
    )
    row_locations_vision = load_json(
        get_latest_file_or_0(f"{row_locations_vision_root}/camera_{camera_id}")
    )
    if given_row_locations_robot is None:
        row_locations_robot_root = (
            f"{config.get_setting('folder_paths', 'calibration_folder')}/row_locations_robot"
        )
        row_locations_robot = load_json(
            get_latest_file_or_0(f"{row_locations_robot_root}/camera_{camera_id}")
        )
    else:
        row_locations_robot = given_row_locations_robot
    row_id = str(box_id)[-2]

    # Start transformation
    markers_image = row_locations_vision[str(row_id)]
    markers_robot = np.asarray(row_locations_robot[str(row_id)])[:, :3]

    # Get x and y coordinates
    robot_pickpoint = get_homography(point, markers_image, markers_robot)

    # Get z coordinate
    robot_pickpoint = get_plane_transform(robot_pickpoint, markers_robot)

    return robot_pickpoint


def transform_coordinate_robot_to_box(point, box_id, camera_id):
    """
    Transform a point from 3D coordinates in the robot world reference frame to 3D coordinates in the box world reference plane
    """
    box_origin_rack = get_box_origin_robot(box_id, camera_id)
    box_point = point - box_origin_rack
    return box_point


def transform_coordinate_box_to_box_frame(point, camera_id):
    """
    Transform a point from 3D coordinates in the box world reference frame to 2D coordinates in the box rack reference plane
    """
    # Get rotation matrix
    R = get_box_frame_transformation(camera_id)

    # Apply rotation inverse
    rotated_point = np.dot(np.linalg.inv(R), point)
    return rotated_point[:3]


def transform_coordinate_box_frame_to_box(point, camera_id):
    """
    Transform a point from 3D coordinates in the box world reference frame to 2D coordinates in the box rack reference plane
    """
    # Get rotation matrix
    R = get_box_frame_transformation(camera_id)

    # Apply rotation
    rotated_point = np.dot(R, point)
    return rotated_point[:3]


def get_box_frame_transformation(camera_id):
    # Get rotations
    angles = get_rotations(camera_id)  # return yaw, pitch, roll
    roll, pitch, yaw = np.radians(
        angles
    )  # For some reason the roll and yaw need to be swapped? Is this due to Fanuc's conventions?

    # Rotation matrices
    R_z = np.array([[np.cos(yaw), -np.sin(yaw), 0], [np.sin(yaw), np.cos(yaw), 0], [0, 0, 1]])
    R_y = np.array(
        [
            [np.cos(pitch), 0, np.sin(pitch)],
            [0, 1, 0],
            [-np.sin(pitch), 0, np.cos(pitch)],
        ]
    )
    R_x = np.array([[1, 0, 0], [0, np.cos(roll), -np.sin(roll)], [0, np.sin(roll), np.cos(roll)]])

    # Combine rotations
    R = np.dot(R_z, np.dot(R_y, R_x))
    return R


def get_box_origin_robot(box_id, camera_id, given_row_locations_robot=None):
    """
    Get the origin (bottom-right corner) of the box as 3D coordinates in the robot world reference plane
    """
    # Load corner data
    box_corners = get_region(box_id, camera_id)

    # Get origin coordinate
    box_origin_image = box_corners[
        3
    ]  # The box_corners are sorted and formatted such that index 3 is the bottom-right corner
    box_origin_robot = transform_coordinate_image_to_robot(
        box_origin_image,
        box_id,
        camera_id,
        given_row_locations_robot=given_row_locations_robot,
    )

    # Offset by the marker height
    marker_height = 15  # mm
    offset_frame = [0, 0, -marker_height]
    offset = transform_coordinate_box_frame_to_box(offset_frame, camera_id)
    box_origin_robot = box_origin_robot + offset

    return box_origin_robot


def get_rotations(camera_id, given_row_locations_robot=None):
    """
    Get the rotations of the rack in degrees in roll, pitch, and yaw, which should be constant for the entire rack
    The rotations are not calibrated during the calibration, and needs to be calculated
    """
    # Load calibration data
    if given_row_locations_robot is None:
        row_locations_robot_root = (
            f"{config.get_setting('folder_paths', 'calibration_folder')}/row_locations_robot"
        )
        row_locations_robot = load_json(
            get_latest_file_or_0(f"{row_locations_robot_root}/camera_{camera_id}")
        )
    else:
        row_locations_robot = given_row_locations_robot

    # Get directions
    steepest_ascent = get_steepest_ascent(row_locations_robot)
    slope = -get_slope(row_locations_robot)
    row_direction = get_row_direction(row_locations_robot)
    angle_difference = steepest_ascent - row_direction
    angle_difference_radians = np.radians(angle_difference)

    # Get rotations (constant for the entire rack)
    yaw = slope * -np.sin(angle_difference_radians)
    pitch = slope * np.cos(angle_difference_radians)
    roll = row_direction
    return [yaw, pitch, roll]


def get_steepest_ascent(row_locations_robot):
    """
    Get the angle in degrees of the steepest ascent
    """
    average_roll = 0
    for row in row_locations_robot.keys():
        # Calculate picking plane
        A, B, C, D = best_fit_plane(np.asarray(row_locations_robot[row])[:, :3])

        # Calculate direction of steepest ascent
        grad_xy = np.array([A, B]) * np.copysign(1, -C)
        roll = np.arctan2(grad_xy[1], grad_xy[0])
        roll = np.degrees(roll)
        average_roll += roll / len(row_locations_robot.keys())

    return roll


def get_row_direction(row_locations_robot):
    """
    Get the angle in degrees of the direction of the rows of the rack
    """
    # Calculate average points
    average_points = [
        np.mean(np.asarray(row_locations_robot[row])[:, :3], axis=0)
        for row in row_locations_robot.keys()
    ]

    # Calculate the centroid of the points
    centroid = np.mean(average_points, axis=0)

    # Subtract the centroid from the points to center them at the origin
    centered_points = average_points - centroid

    # Perform Singular Value Decomposition (SVD) on the centered points
    _, _, vh = np.linalg.svd(centered_points)

    # The direction vector of the best-fit line is given by the first right singular vector
    direction_vector = vh[0]
    if direction_vector[2] < 0:
        direction_vector = -direction_vector

    # Calculate the angle in the x-y plane
    angle_to_xy_plane = np.arctan2(direction_vector[1], direction_vector[0])
    angle_to_xy_plane_degrees = np.degrees(angle_to_xy_plane)

    return angle_to_xy_plane_degrees


def get_slope(row_locations_robot):
    """
    Get the slope of the steepest ascent in order to determine the steepness of the plane.
    Assumes that the z axis perpendicular to the plane is not tilted too much.
    """
    average_slope = 0
    for row in row_locations_robot.keys():
        # Calculate picking plane
        A, B, C, D = best_fit_plane(np.asarray(row_locations_robot[row])[:, :3])

        # Calculate the magnitude of the gradient vector
        grad_xy = np.array([A, B]) * np.copysign(1, -C)
        slope = np.linalg.norm(grad_xy) / abs(C)  # slope = |grad_xy| / |C|
        slope = np.degrees(slope)
        average_slope += slope / len(row_locations_robot.keys())

    return slope


def get_homography(point, pt2d, pt3d):
    # Convert to np
    pt = np.asarray(point)
    pt2d = np.asarray(pt2d)
    pt3d = np.asarray(pt3d)

    # If the mapping is approximately planar, you can estimate a homography
    H, _ = cv.findHomography(pt2d, pt3d[:, :2], method=cv.RANSAC)

    # Homogeneous coordinates
    image_pickpoint_calc = np.array([pt[0], pt[1], 1], dtype=np.float64)

    # Apply homography
    pt_transformed_h = np.dot(H, image_pickpoint_calc.T)

    # Convert back to Cartesian coordinates
    pt_transformed = pt_transformed_h[:2] / pt_transformed_h[2]
    return pt_transformed


def get_plane_transform(point, pt3d):
    # Calculate the plane's equation
    A, B, C, D = best_fit_plane(pt3d)

    # Calculating the z-coordinate based on the plane equation
    z = -(A * point[0] + B * point[1] + D) / C

    # 3D coordinates in the transformed space
    pt_3d_transformed = np.array([point[0], point[1], z])
    return pt_3d_transformed


def best_fit_plane(pt3d):
    """
    Calculate the best fit plane's equation Ax + By + Cz + D = 0 using
    least squares for a set of 3D points.

    Args:
        pt3d (np.array): An nx3 array where each row is a 3D point [X, Y, Z].

    Returns:
        A, B, C, D: Coefficients of the plane equation.
    """
    # Ensure numpy array
    pt3d = np.array(pt3d)

    # Centering the points
    centroid = np.mean(pt3d, axis=0)
    pt3d_centered = pt3d - centroid

    # Singular Value Decomposition (SVD)
    _, _, vh = np.linalg.svd(pt3d_centered)

    # The normal to the plane is the last singular vector
    normal = vh[-1, :]

    # Plane equation Ax + By + Cz + D = 0 coefficients
    A, B, C = normal
    D = -np.dot(normal, centroid)

    return A, B, C, D


def get_region(box_id, camera_id):
    # Load corner data
    box_locations_root = (
        f"{config.get_setting('folder_paths', 'calibration_folder')}/box_locations"
    )
    box_corner_dict = load_json(get_latest_file_or_0(f"{box_locations_root}/camera_{camera_id}"))

    # Get region
    region = np.array(box_corner_dict[str(box_id)])
    region_sorted = sort_points_clockwise(region)
    region_formatted = [
        region_sorted[2],
        region_sorted[1],
        region_sorted[3],
        region_sorted[0],
    ]  # format such that opencv can transform images properly
    return region_formatted


def sort_points_clockwise(points):
    """
    Sorts points clockwise around their center, starting at 12 o'clock.
    :param points: The points to sort
    """
    # Calculate the centroid
    centroid = np.mean(points, axis=0)

    # Calculate the angles relative to the centroid
    angles = np.arctan2(points[:, 0] - centroid[0], points[:, 1] - centroid[1])
    angles = (angles + 2 * np.pi) % (2 * np.pi)

    # Sort the points based on angles
    sorted_indices = np.argsort(angles)
    sorted_points = points[sorted_indices]

    return sorted_points


def main():
    from python_logger import logger

    camera_id = 3
    box_id = "2334"

    # box_image_pickpoint = (0, 0) # is furthest point in frame
    box_image_pickpoint = (200, 240)  # is frame zero point (aside from offset)
    # box_image_pickpoint = (125, 188) # actual pickpoint
    logger.info(f"box_image_pickpoint: {box_image_pickpoint}")
    image_pickpoint = transform_coordinate_box_to_image(box_image_pickpoint, box_id, camera_id)
    logger.info(f"image_pickpoint: {image_pickpoint}")
    robot_pickpoint = transform_coordinate_image_to_robot(image_pickpoint, box_id, camera_id)
    logger.info(f"robot_pickpoint: {robot_pickpoint}")
    box_pickpoint = transform_coordinate_robot_to_box(robot_pickpoint, box_id, camera_id)
    logger.info(f"box_pickpoint: {box_pickpoint}")
    box_frame_pickpoint = transform_coordinate_box_to_box_frame(box_pickpoint, camera_id)
    logger.info(f"box_frame_pickpoint: {box_frame_pickpoint}")
    logger.info(f"rotations: {get_rotations(camera_id)}")
    logger.info(f"box_frame: {get_box_origin_robot(box_id, camera_id)}")


if __name__ == "__main__":
    main()
