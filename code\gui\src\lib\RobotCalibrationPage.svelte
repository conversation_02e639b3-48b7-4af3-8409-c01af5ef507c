<script>
  // Imports
  import { Card, Col, Row } from "sveltestrap";
  import { pythonStore } from "../stores";
  import RobotCalibrationInput from "./RobotCalibrationInput.svelte";
  import ConfirmationModal from "./ConfirmationModal.svelte";
  
  export let index;
  export let calibrationIndex;
  
  const { sendCommand } = pythonStore;

  $: robotCalibrationImage = $pythonStore?.live_images?.['robot_calibration_image'] || null;
  $: robotCalibrationFinished = $pythonStore.data['robot_calibration_finished'] || false;
  $: robotCalibrationCopyable = $pythonStore.data['robot_calibration_copyable'] || false;

  let coordinates = ['X', 'Y', 'Z', 'W', 'P', 'R'];
  let inputs = [null, null, null, null, null, null];
  let showModal = false;

  function calibrateRobot() {
    setTimeout(() => {
      sendCommand("calibrate_robot", inputs);
      inputs = [null, null, null, null, null, null];
    }, 750)
  }

  function calibrateRobotCopy() {
    sendCommand("calibrate_robot_copy");
    inputs = [null, null, null, null, null, null];
  }

  function calibrateRobotNext() {
    sendCommand("calibrate_robot_next");
  }

  function calibrateRobotPrevious() {
    sendCommand("calibrate_robot_previous");
  }

  function cancelCalibration() {
    sendCommand("cancel_calibrate_robot");
    index = 0;
    calibrationIndex = 0;
  }

  function turnBack() {
    sendCommand("log_out");
    index = 0;
    calibrationIndex = 0;
  }

  function showConfirmationModal() {
    showModal = true;
  }

  function confirmCopy() {
    calibrateRobotCopy();
    showModal = false;
  }

  function cancelModal() {
    showModal = false;
  }
</script>

<style>
  button {
    font-size: 0.9em;
    width: 7em;
    height: 2.5em;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    padding: 0 20px;
    min-height: 35px;
    border: 2px solid #55BA2B;
    border-radius: 20px;
    background: #55BA2B;
    color: white;
  }
</style>

<Card title="" color="primary-subtle" class="mt-4" style="display: flex; align-items: center;">
  {#if !robotCalibrationFinished}
    <h6 style="padding-top: 25px; padding-bottom: 8px; padding-left: 8px; padding-right: 8px; text-align: center; font-size: 16px !important;">
      Move the robot gripper to the center of the indicated calibration marker. Afterwards, enter the robot coordinates and click the Calibrate button.
    </h6>
    <Row>
      <Col>
        {#if robotCalibrationImage}
          <img src={robotCalibrationImage} alt="calibration_image" style="margin: 10px; width: 98% !important; max-width: 54em !important"/>
        {/if}
      </Col>
      <Col style="max-width: 8em !important; margin-right: 10px; display: flex; flex-direction: column; justify-content: center;">
        {#each coordinates.slice(0, 3) as coordinate, i}
          <Row>
            <RobotCalibrationInput name={coordinate} bind:givenValue={inputs[i]}/>
          </Row>
        {/each}
        <Row>
            <button
                on:click={calibrateRobot}
                color="success"
                size="lg"
                style="margin: 0px; margin-top: 10px;"
            >
                Calibrate
            </button>
        </Row>
        {#if robotCalibrationCopyable}
          <Row>
            <button
              on:click={showConfirmationModal}
              color="success"
              size="lg"
              style="margin: 0px; margin-top: 10px;"
            >
              Copy
            </button>
          </Row>
        {/if}
      </Col>
    </Row>
    <Row>
      <button
        on:click={calibrateRobotPrevious}
        color="success"
        size="lg"
        style="margin-top: 20px; margin-bottom: 20px; margin-right: 20px; width: 9.5em; background-color:#85C5E0; border: 2px solid #85C5E0;"
      >
        Previous marker
      </button>
      <button
        on:click={calibrateRobotNext}
        color="success"
        size="lg"
        style="margin-top: 20px; margin-bottom: 20px; width: 11.2em; background-color:#85C5E0; border: 2px solid #85C5E0;"
      >
        Skip camera
      </button>
    </Row>
  {:else}
    <h6 style="padding-top: 25px; padding-bottom: 8px; text-align: center; font-size: 16px !important;">
      Calibration finished.
    </h6>
    <button
      on:click={turnBack}
      style="margin-top: 20px; margin-bottom: 20px;"
    >
      Back
    </button>
  {/if}
</Card>
{#if !robotCalibrationFinished}
  <Row style="justify-content: flex-end;">
    <button
      on:click={cancelCalibration}
      color="warning"
      size="lg"
      style="margin-top: 20px; margin-bottom: 20px; width: 5em; margin-right: 0.5em; background-color:#DE9872; border: 2px solid #DE9872;"
    >
      Cancel
    </button>
  </Row>
{/if}

{#if showModal}
  <ConfirmationModal
    message='Are you sure you want to copy the calibration marker cöordinates from the adjacent rack?'
    confirmText='Copy calibration marker'
    cancelText='Cancel'
    onConfirm={confirmCopy}
    onCancel={cancelModal}
  />
{/if}
