import colorsys
from functools import partial

import cv2 as cv
import numpy as np
from numpy.typing import NDArray

from utilities import show


def segment_pickpoint_image(
    pickpoint: tuple[int, int],
    segmentation,
    image: cv.typing.MatLike,
    visualize=False,
) -> cv.typing.MatLike:
    return segment_pickpoints_image([pickpoint], [segmentation], image, visualize=visualize)


def segment_pickpoints_image(
    pickpoints: list[tuple[int, int]],
    segmentations: list[NDArray[np.bool_]],
    image: cv.typing.MatLike,
    visualize=False,
    max_amount_of_pickpoints_to_draw=10,
) -> cv.typing.MatLike:
    if len(pickpoints) == 0 or len(segmentations) == 0:
        return image
    pickpoint_image = get_segmentation_image(segmentations[0], image)
    pickpoints = pickpoints[:max_amount_of_pickpoints_to_draw]
    point_color = partial(scale_color, len(pickpoints))
    for i, pickpoint in enumerate(pickpoints):
        pickpoint_image = cv.circle(pickpoint_image, tuple(pickpoint), 5, point_color(i), -1)
        text_position = (pickpoint[0] + 5, pickpoint[1])
        pickpoint_image = cv.putText(
            pickpoint_image,
            str(i + 1),
            text_position,
            cv.FONT_HERSHEY_SIMPLEX,
            0.5,
            (0, 0, 0),
            3,
            cv.LINE_AA,
        )
        pickpoint_image = cv.putText(
            pickpoint_image,
            str(i + 1),
            text_position,
            cv.FONT_HERSHEY_SIMPLEX,
            0.5,
            (255, 255, 255),
            1,
            cv.LINE_AA,
        )
    show(pickpoint_image, window_name="pickpoint_image", scale=1.5, visualize=visualize)
    return pickpoint_image


def get_segmentation_image(
    segmentation: NDArray[np.bool_],
    image: cv.typing.MatLike,
    visualize=False,
) -> cv.typing.MatLike:
    segmentation_image = segment_background_image(image)
    segmentation_image[segmentation] = image[segmentation]
    show(segmentation_image, window_name="segmentation_image", scale=1.5, visualize=visualize)
    return segmentation_image


def segment_background_image(image: cv.typing.MatLike, visualize=False) -> cv.typing.MatLike:
    background_image = np.array(image.copy() * 0.7, dtype=np.uint8)
    background_image[:, :, 0] = np.clip(background_image[:, :, 0] * 0.8, 0, 255).astype(np.uint8)
    background_image[:, :, 1] = np.clip(background_image[:, :, 1] * 0.4, 0, 255).astype(np.uint8)
    background_image[:, :, 2] = np.clip(background_image[:, :, 2] * 0.6, 0, 255).astype(np.uint8)
    show(background_image, window_name="background_image", scale=1.5, visualize=visualize)
    return background_image


def segment_multiple_segmentations_image(segmentations, image, visualize=False):
    pass


def show_points(image, points):
    pass


def scale_color(amount: float | int, i: float | int) -> tuple[int, int, int]:
    """
    Returns a BGR color tuple for the fraction i/amount,
    where 0 => green, 0.4 => blue, 0.9 => red.

    This ensures:
       scale_color(10,0) => green
       scale_color(10,4) => blue
       scale_color(10,9) => red
    and scale_color(x,y) = scale_color(k*x, k*y).
    """

    # Safeguard: avoid division by zero if amount == 0
    if amount == 0:
        return (0, 255, 0)  # default to green

    # Compute fraction in [0..1]
    ratio = i / float(amount)

    # Define our pivot points in the ratio space
    pivot_blue = 0.4  # ratio at which the hue is exactly 240 (blue)
    pivot_red = 0.9  # ratio at which the hue is exactly 360 (red)

    if ratio < 0.0:
        ratio = 0.0
    elif ratio > 1.0:
        ratio = 1.0

    if ratio <= pivot_blue:
        # Segment A: [0..0.4] => green (120°) -> blue (240°)
        segment_ratio = ratio / pivot_blue  # rescales to [0..1]
        hue_deg = 120 + segment_ratio * (240 - 120)
    elif ratio <= pivot_red:
        # Segment B: [0.4..0.9] => blue (240°) -> red (360°)
        segment_ratio = (ratio - pivot_blue) / (pivot_red - pivot_blue)  # [0..1]
        hue_deg = 240 + segment_ratio * (360 - 240)
    else:
        # Segment C: [0.9..1] => stay at red (360°)
        hue_deg = 360

    # Convert hue in [0..360] => [0..1] for colorsys
    h = hue_deg / 360.0

    # Saturation=1, Value=1 for full-vibrancy color
    r, g, b = colorsys.hsv_to_rgb(h, 1.0, 1.0)

    # Convert from float [0..1] to int [0..255], then to BGR for OpenCV
    return (int(b * 255), int(g * 255), int(r * 255))
