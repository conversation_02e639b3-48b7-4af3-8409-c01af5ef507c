import cv2 as cv
import cProfile
import numpy as np
import time
from typing import Optional, List, TypedDict
from numpy.typing import NDArray


from heliovision.AI.models.SAM.Sam2D import Sam2D
from heliovision.james import RotatedRect

from depth_image import get_depth_image, get_edges_from_depth_image
from gradient_analysis import compute_inward_gradient_score
from python_logger import logger
from utilities import show


class Mask(TypedDict):
    segmentation: np.ndarray
    clean_segmentation: np.ndar<PERSON>
    predicted_iou: float
    multimask_index: int
    rrect: "RotatedRect"
    area: int
    score: Optional[float]
    pickpoint: Optional[tuple[int, int]]
    density: Optional[float]
    background_part: Optional[float]
    gripper_radius: Optional[float]
    inward_gradient_fraction: Optional[float]


class PickpointSegmenter(Sam2D):
    model_folder = "myenv/data"

    def set_image(self, image: np.ndarray, image_format: str = "RGB"):
        # Override in order to delay moving the image to the GPU in case there are no input points
        self.image = image
        self.image_format = image_format

    def create_points(self, background_image, *args, **kwargs) -> np.ndarray:
        points = super().create_points(points_x=11, points_y=13)

        # Filter out background points
        filtered_points = []
        for point in points:
            if background_image[tuple(point[0][::-1])] == 0:
                filtered_points.append(point)
        filtered_points = np.asarray(filtered_points)

        if len(filtered_points) > 0:
            self.predictor.set_image(self.image, self.image_format)

        return filtered_points

    def score_masks(
        self,
        mask: Mask,
        background_image,
        depth_image: NDArray[np.uint8],
        min_area=1_000,
        max_area=10_000,
        *args,
        **kwargs,
    ) -> float:
        """
        Returns a score for a mask.
            score >  0.8 : good pick points
            score >  0.5 : back up pick points
            score < 0.5: don't use
            score <= -inf: has a severe issue
        """
        INF = np.inf
        if mask["predicted_iou"] >= 0.95:
            score = 0.95 + 0.2 * ((mask["predicted_iou"] - 0.95) / 0.05)
        elif mask["predicted_iou"] >= 0.9:
            score = mask["predicted_iou"]
        elif mask["predicted_iou"] >= 0.8:
            score = 0.9 - (0.9 - mask["predicted_iou"]) * 1.5
        else:
            score = 0.8 - 0.05 - (0.8 - mask["predicted_iou"]) * 3

        # Make sure the mask is coherent
        if mask["rrect"].area == 0:
            score -= INF
            mask["density"] = 0
            return score
        else:
            density = mask["area"] / mask["rrect"].area
            mask["density"] = density
            if density < 0.5:
                score -= INF
            elif density < 0.75:
                score -= 0.15 + (0.75 - density) * 4
            elif density < 0.8:
                score -= 0.05 + (0.8 - density) * 2
            elif density < 0.85:
                score -= 0.85 - density

        # Make sure the mask is in the proper size range
        if mask["area"] > max_area * 1.5:
            score -= INF
            return score
        elif mask["area"] > max_area:
            score -= 0.15
        elif mask["area"] < min_area * 0.75:
            score -= 0.5
        elif mask["area"] < min_area:
            score -= 0.2 * min((min_area - mask["area"]) / (min_area * 0.25), 1)
        else:
            score += 1 * min((mask["area"] - min_area) / (max_area - min_area), 0.3)

        # Make sure the mask is not the background
        if background_image is not None:
            mask_background = background_image * mask["clean_segmentation"]
            count = np.count_nonzero(mask_background)
            if mask["area"] == 0:
                score -= INF
                return score
            else:
                background_part = count / mask["area"]
                mask["background_part"] = background_part
                if background_part > 0.2:
                    score -= INF
                elif background_part > 0.05:
                    score -= (background_part - 0.05) * 4

        # Make sure the gripper has the proper area to grip
        pickpoint_center = mask["pickpoint"]
        if pickpoint_center is None:
            score -= INF
            mask["gripper_radius"] = 0
            return score
        else:
            gripper_radius = largest_circle_radius(mask["clean_segmentation"], pickpoint_center)
            if gripper_radius < 10:
                score -= INF
            elif gripper_radius < 13:
                score = score
            elif gripper_radius < 30:
                score += 0.6 * min((gripper_radius - 13) / 17, 1)
            else:
                score += 0.6 + 0.15 * ((gripper_radius - 30) / 5)
            mask["gripper_radius"] = gripper_radius

        # Get a height score
        depth_values = depth_image[mask["clean_segmentation"]]
        if depth_values.size == 0:
            score -= INF
            return score
        else:
            min_depth = int(np.min(depth_values))
            avg_depth = int(np.mean(depth_values))
            score += min_depth / 255 + avg_depth / 255

        # Check if the gradient at the edge points inward (indicating object boundaries)
        inward_gradient_score = compute_inward_gradient_score(
            mask["clean_segmentation"], depth_image
        )
        mask["inward_gradient_fraction"] = inward_gradient_score
        score += inward_gradient_score * 2 - 0.5

        return score

    def filter_masks(self, masks: List[Mask], min_score=0.4, *args, **kwargs) -> List[Mask]:
        new_pixels = np.ones(
            self.image.shape[:2], dtype=bool
        )  # Keep track of which pixels were already assigned to a mask
        filtered_masks = []  # Keep track of which masks are good
        for mask in masks:
            if mask["score"] is None or mask["score"] < min_score:
                break

            # Check if mask overlaps with old masks too much
            if (
                np.count_nonzero(np.logical_and(mask["clean_segmentation"], ~new_pixels))
                > mask["area"] * 0.2
            ):
                continue

            # Check if mask can be split into smaller good segmentations of individual parts
            split = False
            for mask2 in masks:
                if mask2["area"] >= mask["area"]:
                    continue
                if (
                    np.count_nonzero(
                        np.logical_and(mask["clean_segmentation"], mask2["clean_segmentation"])
                    )
                    > mask["area"] * 0.2
                ):
                    if mask2["score"] is not None and mask2["score"] >= max(
                        min_score, mask["score"] - 0.3
                    ):
                        split = True
                        break
            if split:
                continue

            new_pixels[mask["clean_segmentation"]] = False
            filtered_masks.append(mask)

        return filtered_masks

    def get_mask_features(self, segmentations, iou_scores) -> List[Mask]:
        masks = []
        i = 0
        for segmentation, iou_score in zip(segmentations, iou_scores):
            clean_segmentation = np.zeros(segmentation.shape, dtype=np.uint8)
            clean_segmentation[segmentation > 0] = 255
            clean_segmentation = cv.copyMakeBorder(
                clean_segmentation, 5, 5, 5, 5, cv.BORDER_CONSTANT, value=(0,)
            )  # buffer for edges
            clean_segmentation = cv.morphologyEx(
                clean_segmentation,
                cv.MORPH_CLOSE,
                np.ones((3, 3), np.uint8),
                iterations=1,
            )
            clean_segmentation = cv.morphologyEx(
                clean_segmentation,
                cv.MORPH_OPEN,
                np.ones((3, 3), np.uint8),
                iterations=2,
            )
            clean_segmentation = clean_segmentation[5:-5, 5:-5]  # Restore size
            clean_segmentation = clean_segmentation.astype(bool)
            try:
                rotated_rect = RotatedRect.create_from_image(clean_segmentation)
            except Exception:
                rotated_rect = RotatedRect((0, 0), (0, 0), 0)
            mask = {
                "segmentation": segmentation,
                "clean_segmentation": clean_segmentation,
                "predicted_iou": iou_score,
                "multimask_index": i
                % 2,  # Determine multimask index (always three multimask starting with the biggest)
                "rrect": rotated_rect,
                "area": np.count_nonzero(clean_segmentation),
                "score": None,
                "density": None,
                "background_part": None,
                "gripper_radius": None,
                "inward_gradient_fraction": None,
            }
            mask["pickpoint"] = find_center(mask["clean_segmentation"])
            masks.append(mask)
            i += 1

        return masks

    def get_pickpoints(self, image, background_image=None, visualize=False, verbose=False):
        # Get depth image
        depth_image = get_depth_image(image)
        show(depth_image, "depth_image", scale=1.5, visualize=visualize)

        # Clean up image
        image = cv.blur(image, (3, 3))
        image = enhance(image)
        image = cv.blur(image, (3, 3))
        image = enhance(image)

        # Add extra edges from depth image
        extra_edges = get_edges_from_depth_image(depth_image)
        image[extra_edges == 255] = (0, 0, 0)
        show(extra_edges, "extra_edges", scale=1.5, visualize=visualize)
        show(image, "image", scale=1.5, visualize=visualize)

        # Get masks
        t0 = time.perf_counter()
        masks = self.get_masks(image, depth_image=depth_image, background_image=background_image)
        t1 = time.perf_counter()
        logger.info(f"Segmentation time spent: {t1 - t0} seconds")

        # Visualize
        visualisation_image = None
        if visualize:
            visualisation_image = create_background_image(image)

        # Iterate over masks
        for mask in masks:
            mask["pickpoint"] = find_center(mask["clean_segmentation"])
            if mask["pickpoint"] is None:
                continue

            if verbose:
                logger.debug("====================")
                logger.debug(f"Area: {mask['area']:.0f}")
                logger.debug(f"Predicted IoU: {mask['predicted_iou']:.3f}")
                logger.debug(f"Final score: {mask['score']:.3f}")
                logger.debug(f"Center: {mask['pickpoint']}")
                logger.debug(f"Rect: {mask['rrect']}")
                logger.debug(f"Density: {mask['density']}")
                logger.debug(f"Background: {mask['background_part']}")
                logger.debug(f"Gripper Radius: {mask['gripper_radius']}")
                logger.debug(f"Inward Gradient Fraction: {mask['inward_gradient_fraction']}")
                logger.debug(f"Multimask Index: {mask['multimask_index']}")

            if visualize and visualisation_image is not None:
                visualisation_image[mask["clean_segmentation"]] = depth_image[
                    mask["clean_segmentation"]
                ]

                cv.circle(visualisation_image, tuple(mask["pickpoint"]), 5, (255, 0, 0), -1)
                # show double size
                visualisation_image_2 = cv.resize(visualisation_image, (0, 0), fx=1.5, fy=1.5)
                show(visualisation_image_2, "Result", visualize=visualize)
                visualisation_image[mask["clean_segmentation"]] = (
                    image[mask["clean_segmentation"]] * 0.25
                )

            yield (mask["pickpoint"], mask["clean_segmentation"])


def create_background_image(image):
    background_image = np.array(image.copy() * 0.25, dtype=np.uint8)
    background_image[:, :, 0] //= 2
    background_image[:, :, 1] //= 2
    return background_image


# taken from https://stackoverflow.com/a/41075028
def enhance(img):
    # converting to LAB color space
    lab = cv.cvtColor(img, cv.COLOR_BGR2LAB)
    l_channel, a, b = cv.split(lab)

    # Applying CLAHE to L-channel
    # feel free to try different values for the limit and grid size:
    clahe = cv.createCLAHE(clipLimit=1.5, tileGridSize=(8, 8))
    cl = clahe.apply(l_channel)

    # merge the CLAHE enhanced L-channel with the a and b channel
    limg = cv.merge((cl, a, b))

    # Converting image from LAB Color model to BGR color spcae
    enhanced_img = cv.cvtColor(limg, cv.COLOR_LAB2BGR)

    return enhanced_img


def find_center(mask) -> Optional[tuple[np.ndarray]]:
    """
    Find the center of a mask

    Parameters:
    - mask: 2D bool NumPy array of booleans where True indicates the mask region.

    Returns:
    - center: Tuple (col, row) indicating the center of the mask's largest inscribed circle.
    """
    # Convert the boolean mask to uint8 image (0 and 255)
    mask_uint8 = (mask * 255).astype(np.uint8)
    mask_uint8 = cv.copyMakeBorder(
        mask_uint8, 5, 5, 5, 5, cv.BORDER_CONSTANT, value=(0,)
    )  # buffer for edges
    if not np.any(mask_uint8):
        return None

    # Compute the distance transform
    distance = cv.distanceTransform(mask_uint8, distanceType=cv.DIST_L2, maskSize=5)

    # Find the maximum distance value
    max_distance = np.max(distance)
    if max_distance == 0:
        return None
    distance = distance[5:-5, 5:-5]  # Restore size
    points = np.argwhere(distance == max_distance)
    center = points[0][::-1]  # Reverse to get (col, row)

    return tuple(center)


def largest_circle_radius(mask, point):
    """
    Compute the radius of the largest circle centered at 'point' that fits within 'mask'.

    Parameters:
    - mask: 2D bool NumPy array of booleans where True indicates the mask region.
    - point: Tuple (row, col) in pixel coordinates indicating the center of the circle.

    Returns:
    - radius: The radius of the largest fitting circle.
    """
    mask_uint8 = (mask * 255).astype(np.uint8)

    # Compute the distance transform
    distance = cv.distanceTransform(mask_uint8, distanceType=cv.DIST_L2, maskSize=5)

    # Extract the radius at the given point
    row, col = point
    radius = distance[col, row]

    return radius


def pickpoint_test():
    from image_transformations import transform_and_crop_to_box, create_background_mask
    from visualisations import segment_pickpoints_image
    from config_loader import config

    camera_id = 3
    image_path = "data/2024-12-06_13-51-27-034583_box_1334.png"
    robot_id = int(config.get_setting("other", "robot_id"))
    pickpoint_segmenter = PickpointSegmenter(size="vit_b")

    # Get image
    image = cv.imread(image_path)
    # show(image, 'original', scale=0.5)
    total_time = 0

    # Calculate for each of the boxes
    for i in [
        11,
        12,
        13,
        14,
        15,
        21,
        22,
        23,
        24,
        25,
        31,
        32,
        33,
        34,
        35,
        41,
        42,
        43,
        44,
        45,
        51,
        52,
        53,
        54,
        55,
    ]:
        # for i in [
        #     34
        # ]:
        box_id = 1000 * robot_id + 100 * camera_id + i

        # Crop to box
        box_image = transform_and_crop_to_box(image, box_id, camera_id)

        # Determine background
        mask_full = create_background_mask(image, visualize=False)
        box_background_mask = transform_and_crop_to_box(
            mask_full, box_id, camera_id, visualize=False
        )
        # show(box_background_mask, window_name='box_background_mask2', visualize=True)

        # Get pickpoints
        logger.debug(f"Calculating pickpoints {box_id}")
        start = time.perf_counter()
        pickpoint_yielder = pickpoint_segmenter.get_pickpoints(
            box_image,
            background_image=box_background_mask,
            visualize=True,
            verbose=False,
        )
        result_list = list(pickpoint_yielder)
        end = time.perf_counter()
        logger.info(f"Time spent: {end - start} seconds")
        total_time += end - start
        if len(result_list) == 0:
            logger.debug(f"No pickpoints found for {box_id}")
            continue
        segmentation = [result_list[0][1]]
        pickpoints = [pickpoint for pickpoint, segmentation in result_list]
        segment_pickpoints_image(pickpoints, segmentation, box_image, False)

    # Finish
    logger.debug("done")
    logger.debug(f"Total time spent: {total_time} seconds")


def pickpoint_single_box(box_image):
    from image_transformations import create_background_mask
    from visualisations import segment_pickpoints_image

    box_background_mask = create_background_mask(box_image, visualize=False)
    pickpoint_segmenter = PickpointSegmenter(size="vit_b")
    pickpoint_yielder = pickpoint_segmenter.get_pickpoints(
        box_image,
        background_image=box_background_mask,
        visualize=False,
        verbose=True,
    )
    result_list = list(pickpoint_yielder)
    segmentation = [result_list[0][1]]
    pickpoints = [pickpoint for pickpoint, segmentation in result_list]
    segment_pickpoints_image(pickpoints, segmentation, box_image, True)


def save_results(box_id, image, pickpoints):
    import os
    from utilities import datetime_string_to_day, get_timestamp
    from config_loader import config

    # Save the analyzed image
    directory_root = (
        config.get_setting("folder_paths", "storage_folder")
        + "/live_picks/"
        + datetime_string_to_day(get_timestamp())
        + "/"
    )
    camera_id = str(box_id)[1]
    subdirectory = str(camera_id) + "/"
    directory = directory_root + subdirectory
    if not os.path.exists(directory):
        os.makedirs(directory)
    image_file_name = f"{get_timestamp()}_box_{box_id}.png"
    logger.debug(f"PICKING PROCESS: Saving picking image: {image_file_name}")
    logger.debug(f"PICKING PROCESS: pickpoints: {pickpoints}")
    cv.imwrite(f"{directory}{image_file_name}", image)


if __name__ == "__main__":
    # cProfile.run("pickpoint_test()", sort="cumtime")
    pickpoint_test()
    # pickpoint_single_box(cv.imread("data/floorp_kDpbVXCJff.png"))
