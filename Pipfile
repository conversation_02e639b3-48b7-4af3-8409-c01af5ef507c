[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[[source]]
url = "https://download.pytorch.org/whl/cu118/"
verify_ssl = true
name = "pytorch"

[[source]]  
url = "https://__token__:${HV_PYPI_TOKEN}@git.heliovision.be/api/v4/groups/32/-/packages/pypi/simple"
verify_ssl = true  
name = "hv-pypi"

[packages]
numpy = "*"
opencv-python = "*"
onvif-zeep = "*"
httpx = "*"
fastapi = "*"
uvicorn = "*"
loguru = "*"
torch = { index = "pytorch"}
torchvision = { index = "pytorch"}
segment-anything = {git = "https://github.com/facebookresearch/segment-anything.git"}
python-svelte = { git = "git+ssh://**********************/heliovision/toolbox/python_svelte.git", ref = "v2.3.3" }
james = {version = "*", index = "hv-pypi"}
"heliovision.disk-cleaner" = {version = "*", index = "hv-pypi"}
"heliovision.config-system" = {version = "*", index = "hv-pypi"}
"heliovision.james" = { version = "*", index = "hv-pypi"}
"heliovision.AI.models.SAM" = { version = "*", index = "hv-pypi"}
hikvisionapi = "*"
transformers = "*"

[dev-packages]
ipykernel = "*"
mypy = "*"
pytest = "*"
ruff = "==0.9.10"

[requires]
python_version = "3.10"
