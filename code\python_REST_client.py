import asyncio
import httpx
import traceback
from datetime import datetime
from typing import Optional

from utilities import put_in_bounded_queue, get_timestamp
from python_logger import logger


class RESTClient:
    def __init__(self, pcdata_ip="http://localhost:2420"):
        self.pcdata_ip = pcdata_ip
        self.client = httpx.AsyncClient()

    async def send(self, endpoint, request_type, json_data=None) -> Optional[dict]:
        try:
            if request_type == "GET":
                if json_data is not None:
                    logger.warning("Ignoring submitted json data for GET request")
                response = await self.client.get(f"{self.pcdata_ip}/{endpoint}")
            elif request_type == "POST":
                response = await self.client.post(f"{self.pcdata_ip}/{endpoint}", json=json_data)
            elif request_type == "PUT":
                response = await self.client.put(f"{self.pcdata_ip}/{endpoint}", json=json_data)
            elif request_type == "DELETE":
                response = await self.client.delete(f"{self.pcdata_ip}/{endpoint}")
                if json_data is not None:
                    logger.warning("Ignoring submitted json data for DELETE request")
        except Exception as e:
            logger.error(f"Error for {request_type} {endpoint}: {traceback.format_exc()}")
            raise e

        if response.status_code != 200:
            await self.raise_error(f"{request_type} {endpoint}", response)

        try:
            return response.json()
        except Exception as e:
            if request_type == "GET":
                logger.error(
                    f"Error parsing response from {request_type} {endpoint}: {response}: {traceback.format_exc()}"
                )
                raise e
            else:
                # logger.debug(f"Error parsing response from {request_type} {endpoint}: {response}: {traceback.format_exc()}")
                return None

    @staticmethod
    async def raise_error(endpoint, response):
        try:
            message = response.json()["message"]
        except Exception:
            try:
                message = response.text
            except Exception:
                message = None
        logger.error(
            f"Failed to {endpoint}. Status code: {response.status_code}, Message: {message}"
        )
        raise Exception(
            f"Failed to {endpoint}. Status code: {response.status_code}, Message: {message}"
        )

    async def send_pickpoints(self, box_id, pickpoints, timestamp):
        pickpoints_formatted = [{"x": pickpoint[0], "y": pickpoint[1]} for pickpoint in pickpoints]
        timestamp_formatted = datetime.strptime(timestamp, "%Y-%m-%d_%H-%M-%S-%f")
        timestamp_formatted = timestamp_formatted.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        json_data = [
            {"boxId": box_id, "pickPoints": pickpoints_formatted, "timestamp": timestamp_formatted}
        ]
        await self.send("box/pick_points", "POST", json_data=json_data)

    async def send_box_positions(self, box_positions):
        await self.send("box/position", "PUT", json_data=box_positions)

    async def heartbeat_loop(self, gui_queue, connected):
        connected.value = True
        while True:
            await asyncio.sleep(1)
            try:
                response = await self.send("heartbeat", "GET")
                if response != "OK":
                    raise Exception
                if not connected.value:
                    logger.info("Reconnected with PC Data")
                    connected.value = True
            except Exception:
                if connected.value:
                    logger.error("Connection with PC Data was broken!")
                    connected.value = False
                    put_in_bounded_queue(
                        gui_queue,
                        {
                            "type": "error_log",
                            "error_message": "Connection with the PC Data program was broken!",
                            "timestamp": get_timestamp(format="%Y-%m-%d_%Hh:%Mm:%Ss"),
                        },
                    )


async def main_test():
    rest_client = RESTClient()
    logger.info("starting communications")

    # Heartbeat
    import multiprocessing
    from ctypes import c_bool
    from utilities import start_as_thread

    gui_queue = multiprocessing.Queue()
    connected = multiprocessing.Value(c_bool, True)
    start_as_thread(await rest_client.heartbeat_loop(gui_queue, connected))

    # # Box Positions
    # from calibration import get_box_positions
    # camera_id = 1
    # box_positions = get_box_positions(camera_id)
    # await rest_client.send_box_positions(box_positions)

    # Pickpoints
    # box_id = 1131
    # pickpoints = [
    #     [
    #         1,
    #         1
    #     ],
    #     [
    #         2,
    #         2
    #     ],
    #     [
    #         3,
    #         3
    #     ],
    # ]
    # timestamp = get_timestamp()
    # await rest_client.send_pickpoints(box_id, pickpoints, timestamp)

    logger.info("communications finished")
    await asyncio.Future()


if __name__ == "__main__":
    asyncio.run(main_test())
