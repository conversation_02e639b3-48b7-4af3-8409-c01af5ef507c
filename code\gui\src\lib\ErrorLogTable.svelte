<script>
  // Imports
  import { StyledTable } from "@heliovision/sveltestrap";
  import { pythonStore } from "../stores";
  import { onMount } from 'svelte';

  export let style = '';
  
  let tableHeaders = ['Description', 'Timestamp']
  $: tableData = $pythonStore.data['errorLogs'] || [
    ['Communicatie met heartbeat is verbroken!', '2024-03-21_11h:56m:03s'], 
    ['Geen pickpoint in bak nummer 234 gevonden!', '2024-03-17_23h:41m:29s'], 
    ['Camera verbinding verbroken!', '2024-02-28_08h:20m:48s'], 
    ['Geen pickpoint in bak nummer 311 is gevonden!', '2024-02-22_15h:03m:41s'], 
    ['Camera verbinding verbroken!', '2024-02-16_11h:51m:23s'],  
    ['Camera verbinding verbroken!', '2024-02-16_11h:51m:23s'],  
    ['Camera verbinding verbroken!', '2024-02-16_11h:51m:23s'],  
    ['Camera verbinding verbroken!', '2024-02-16_11h:51m:23s'], 
  ];

  $: currentTime = new Date();

  function isRed(timestamp, currentTime) {
    const timestampDate = new Date(timestamp.replace('_', 'T').replace('h:', ':').replace('m:', ':').replace('s', '') + (timestamp.includes('s') ? '' : ':00'));
    console.log(currentTime - timestampDate)
    return (currentTime - timestampDate) < 1000*60*60; // 1 hour
  }

  // Setup an interval to update the currentTime every minute
  onMount(() => {
    const interval = setInterval(() => {
      currentTime = new Date(); // This update triggers reactivity
    }, 1000*60); // 1 minute

    return () => {
      clearInterval(interval); // Clean up the interval on component destruction
    };
  });
</script>

<StyledTable 
  style={style + '; border-color: #55BA2B; padding: 0px;'}
  tableHeaders={tableHeaders} 
  tableData={tableData} 
  conditions={[{
      condition: row => isRed(row[1], currentTime),
      color: '#FF9B9B',
    },
  ]}>
</StyledTable>
  