import cv2 as cv
import numpy as np
import math

from utilities import show
from python_logger import logger
from config_loader import config


def detect_markers(image, visualize=False, output_image=None):
    show(image, "image", 0.5, visualize)

    # Define the range of marker color (We don't use HSV because it has worse resolution in the camera)
    marker_brightness_factor = config.get_setting("code_parameters", "marker_brightness_factor")
    lower_color = np.array([22.0, 79.0, 166.0]) * (
        marker_brightness_factor - 0.2
    )  # Lower bound for marker color
    upper_color = (
        np.array([96.0, 162.0, 201.0]) * marker_brightness_factor
    )  # Upper bound for marker color

    # Create a mask to only capture areas of the image precisely within the marker color range
    precise_mask = cv.inRange(image, lower_color, upper_color)
    precise_mask = cv.copyMakeBorder(
        precise_mask, 30, 30, 30, 30, cv.BORDER_CONSTANT, value=(0,)
    )  # buffer for edges
    show(precise_mask, "precise_mask", 0.5, visualize)

    # Clean up noisy gaps in marker
    precise_mask = cv.dilate(precise_mask, np.ones((3, 3), np.uint8), iterations=7)
    precise_mask = cv.erode(precise_mask, np.ones((3, 3), np.uint8), iterations=4)
    show(precise_mask, "precise_mask", 0.5, visualize)

    # Create a mask to only capture areas of the image within the marker color range
    extra_mask = cv.inRange(image, lower_color * 0.9, upper_color * 1.3)
    extra_mask = cv.copyMakeBorder(
        extra_mask, 30, 30, 30, 30, cv.BORDER_CONSTANT, value=(0,)
    )  # buffer for edges
    show(extra_mask, "extra_mask", 0.5, visualize)

    # Combine
    mask = cv.bitwise_and(precise_mask, extra_mask)
    show(mask, "mask", 0.5, visualize)

    # # Only look at region of interest
    # mask[:, :200] = 0
    mask[:, 1000:1560] = 0
    # mask[:, 2360:] = 0
    show(mask, "mask", 0.5, visualize)

    # Clean up noisy gaps in marker
    mask = cv.dilate(mask, np.ones((3, 3), np.uint8), iterations=2)
    mask = cv.erode(mask, np.ones((3, 3), np.uint8), iterations=2)
    show(mask, "mask", 0.5, visualize)

    # Clean up noisy background
    mask = cv.erode(mask, np.ones((3, 3), np.uint8), iterations=2)
    mask = cv.dilate(mask, np.ones((3, 3), np.uint8), iterations=2)
    show(mask, "mask", 0.5, visualize)

    # # Clean up noisy gaps in marker
    # mask = cv.dilate(mask, np.ones((3, 3), np.uint8), iterations=5)
    # mask = cv.erode(mask, np.ones((3, 3), np.uint8), iterations=5)
    # show(mask, 'mask', 0.5, visualize)

    # Clean up noisy branches on markers
    mask = cv.erode(mask, np.ones((3, 3), np.uint8), iterations=7)
    mask = cv.dilate(mask, np.ones((3, 3), np.uint8), iterations=7)
    show(mask, "mask", 0.5, visualize)

    # Create a mask of the marker lines
    mask_lines = extract_dark_lines(image)
    show(mask_lines, "mask_lines", 0.5, visualize)

    # Find circular contours in the mask
    mask = mask[30:-30, 30:-30]  # Clean up buffer
    contours = get_circular_contours(mask, 47, 19, 0.65)

    # Detect markers by lines
    result_image = image.copy()
    detected_markers = []
    for contour in contours:
        marker_mask = np.zeros_like(mask)
        marker_mask = cv.drawContours(
            marker_mask,
            [
                contour["contour"],
            ],
            -1,
            (255,),
            thickness=cv.FILLED,
        )

        # Check the target lines
        mask_marker_lines = cv.bitwise_and(mask_lines, marker_mask)
        lines_count = np.count_nonzero(mask_marker_lines)
        lines_metric = lines_count / contour["radius"]
        if lines_metric < 11 or lines_metric > 36:
            # No target lines visible
            logger.debug(f"Contour {contour['center']} discarded for lines_metric: {lines_metric}")
            show(marker_mask, "lines_metric", 0.5, visualize)
            continue

        # Check ellipticalness
        ellipse = cv.fitEllipse(contour["contour"])
        ellipse_axes = ellipse[1]
        ellipticalness = abs(ellipse_axes[0] - ellipse_axes[1]) / (
            (ellipse_axes[0] + ellipse_axes[1]) / 2
        )
        if ellipticalness > 0.19:
            # not circular but elliptical
            logger.debug(
                f"Contour {contour['center']} discarded for ellipticalness: {ellipticalness}"
            )
            show(marker_mask, "elliptical", 0.5, visualize)
            continue

        # Return circle
        logger.debug(contour["radius"] * 2)
        detected_markers.append(contour["center"])

    # Remove mirrored markers
    mirrored = []
    for i in range(len(detected_markers)):
        for j in range(i + 1, len(detected_markers)):
            marker1 = detected_markers[i]
            marker2 = detected_markers[j]
            distance = math.sqrt((marker1[0] - marker2[0]) ** 2 + (marker1[1] - marker2[1]) ** 2)
            if (distance >= 300) or (abs(marker1[1] - marker2[1]) >= 10):
                continue
            logger.debug(f"Mirrored marker! {marker1}, {marker2}")
            if (marker1[0] <= 1280 and marker1[0] <= marker2[0]) or (
                marker1[0] > 1280 and marker1[0] >= marker2[0]
            ):
                mirrored.append(i)
            else:
                mirrored.append(j)
    detected_markers = [
        detected_markers[i] for i in range(len(detected_markers)) if i not in mirrored
    ]

    # Visualize result
    for marker in detected_markers:
        cv.circle(result_image, marker, 28, (0, 255, 0), 3)
    show(result_image, "result_image", 0.5, visualize)
    if output_image is not None:
        output_image[:] = result_image

    # Return the mask and the result image for further processing if necessary
    logger.debug(detected_markers)
    return detected_markers


def get_circular_contours(
    mask, expected_diameter=45, diameter_margin=15, circularity_threshold=0.5, visualize=False
):
    # Find contours in the mask
    contours, _ = cv.findContours(mask, cv.RETR_TREE, cv.CHAIN_APPROX_SIMPLE)
    if visualize:
        result_image = mask.copy()
        result_image = cv.cvtColor(result_image, cv.COLOR_GRAY2BGR)

    # Detect circular contours
    area_range = (
        np.pi * ((expected_diameter - diameter_margin) / 2) ** 2,
        np.pi * ((expected_diameter + diameter_margin) / 2) ** 2,
    )
    circular_contours = []
    for contour in contours:
        # Check the size
        area = cv.contourArea(contour)
        if not (area_range[0] < area < area_range[1]):
            continue

        # Check the circularity
        perimeter = cv.arcLength(contour, True)
        circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter != 0 else 0
        if circularity < circularity_threshold:
            continue
        (x, y), radius = cv.minEnclosingCircle(contour)
        center = (int(x), int(y))
        radius = int(radius)

        # Return circle
        circular_contours.append({"contour": contour, "center": center, "radius": radius})
        if visualize:
            cv.circle(result_image, center, radius, (0, 255, 0), 3)  # type: ignore[possibly-unbound]

    if visualize:
        show(result_image, "circles", 0.5, visualize=True)  # type: ignore[possibly-unbound]
    return circular_contours


def extract_dark_lines(image):
    # Convert to grayscale
    gray = cv.cvtColor(image, cv.COLOR_BGR2GRAY)

    # Apply adaptive thresholding
    adaptive_thresh = cv.adaptiveThreshold(
        gray, 255, cv.ADAPTIVE_THRESH_GAUSSIAN_C, cv.THRESH_BINARY_INV, 35, 10
    )

    return adaptive_thresh


def validate_markers(detected_markers, used_logger=logger):
    # Check total number of markers
    if len(detected_markers) != 20:
        used_logger.warning(
            f"Instead of 20 markers, {len(detected_markers)} markers were detected!"
        )
        return False

    # Check distribution of markers
    left_markers = sorted(
        [marker for marker in detected_markers if marker[0] <= 1280], key=lambda x: x[1]
    )
    right_markers = sorted(
        [marker for marker in detected_markers if marker[0] > 1280], key=lambda x: x[1]
    )
    if len(left_markers) != 10:
        used_logger.warning(
            f"Instead of 10 markers on the left side, {len(left_markers)} markers were detected on the left side!"
        )
        return False

    # Check spacing between markers
    distances_left = [j[1] - i[1] for i, j in zip(left_markers[:-1], left_markers[1:])]
    distances_right = [j[1] - i[1] for i, j in zip(right_markers[:-1], right_markers[1:])]
    distances = distances_left + distances_right
    if min(distances) < 20:
        used_logger.warning("Markers are too close together!")
        return False

    return True


def format_markers(detected_markers):
    left_markers = sorted(
        [marker for marker in detected_markers if marker[0] <= 1280], key=lambda x: x[1]
    )
    right_markers = sorted(
        [marker for marker in detected_markers if marker[0] > 1280], key=lambda x: x[1]
    )
    corner_dict = {}
    for row_nb in range(5):
        row_corners = [
            left_markers[0 + row_nb * 2],
            left_markers[1 + row_nb * 2],
            right_markers[0 + row_nb * 2],
            right_markers[1 + row_nb * 2],
        ]
        corner_dict[row_nb + 1] = row_corners
    return corner_dict


def main_test(image):
    detected_markers = detect_markers(image, visualize=True)
    if not validate_markers(detected_markers):
        raise Exception("Markers invalid")
    detected_row_locations = format_markers(detected_markers)
    detected_markers = [marker for row in detected_row_locations.values() for marker in row]
    logger.info(f"Markers: {detected_markers}")


if __name__ == "__main__":
    image = cv.imread(r"data/images/storage/2024-08-23_11-29-24-748771.png")
    main_test(image)
