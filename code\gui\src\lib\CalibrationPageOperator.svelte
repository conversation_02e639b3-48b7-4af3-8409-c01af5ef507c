<script>
  // Imports
  import { Card, Col, Row } from "sveltestrap";
  import { pythonStore } from "../stores";
  
  export let index;
  export let calibrationIndex;

  const { sendCommand } = pythonStore;

  function turnBack() {
    sendCommand("log_out");
    index = 0;
    calibrationIndex = 0;
  }

  function chooseBoxCalibration() {
    sendCommand("update_box_calibration_image");
    calibrationIndex = 1;
  }
  
  function chooseLogin() {
    calibrationIndex = 3;
  }
</script>

<style>
  button {
    font-size: 0.9em;
    width: 7em;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    padding: 0 20px;
    min-height: 35px;
    border: 2px solid #55BA2B;
    border-radius: 20px;
    background: #55BA2B;
    color: white;
  }
</style>

<Col style="margin-left: 0.5em; margin-right: 0.5em; margin-top: 0px;">  
  <Row>
    <Card title="" color="primary-subtle" class="mt-4" style="display: flex; align-items: center;">
      <Col>
        <Row>
          <h6 style="padding-top: 25px; padding-bottom: 8px; text-align: center; font-size: 16px;">
            Choose which maintenance to start:
          </h6>
        </Row>
        <Row style="justify-content: center; align-items: center;">
          <Col>
            <button
              on:click={chooseBoxCalibration}
              color="success"
              size="lg"
              style="margin-top: 20px; margin-bottom: 20px; width: 11.4em; border-radius: 15px;"
            >
              Camera-Box Calibration
            </button>
          </Col>
          <Col>
            <button
              on:click={chooseLogin}
              color="success"
              size="lg"
              style="margin-top: 20px; margin-bottom: 20px; width: 11.4em; border-radius: 15px;"
            >
              Login
            </button>
          </Col>
        </Row>
      </Col>
    </Card>
  </Row>
  <Row style="justify-content: flex-end;">
    <button
      on:click={turnBack}
      size="lg"
      style="margin-top: 20px; margin-bottom: 20px; background-color:#85C5E0; width: 5em; border: 2px solid #85C5E0;"
    >
      Back
    </button>
  </Row>
</Col>

